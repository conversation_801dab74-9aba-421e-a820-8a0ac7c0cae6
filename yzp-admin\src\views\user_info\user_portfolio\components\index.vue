<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import TransferUserSelect from "@/components/TransferUserSelect/index.vue";
import AuditStatusRecords from "@/components/AuditStatusRecords/index.vue";
import ReasonEditableCard from "@/components/ReasonEditableCard/index.vue";
import VuePdfEmbed from "vue-pdf-embed";
import { formatTimestamp } from "@/utils/dateFormat";
import { handlePortfolioAudit } from "@/api/user_section";
import { portfolioOptions } from "@/utils/quickReply";

const loading = ref<boolean>(false);
const pdfLoading = ref<boolean>(false);
const avatarUrl = ref<string>("");
const rightType = ref<any>("note");
const noteText = ref("");
const selectedValue = ref("");
const infoTableData = ref<any>([]);
const attachmentUrl = ref<string>("");

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

const handlePortfolioAuditApi = async (val: any) => {
  const res: any = await handlePortfolioAudit({ ...val });
  if (res.code === 0) {
    ElMessage.success("操作成功");
  } else {
    ElMessage.error(res.msg || "操作失败");
  }
};

// 副作用：监听 isAudit，赋值给 showResult 和 showTransfer
watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

async function handleConfirm() {
  if (!selectedValue.value) {
    ElMessage.warning("请选择审核结果");
    return;
  }

  // 根据selectedValue判断status
  // agree表示通过，status为3
  // reject表示驳回，status为2
  const status = selectedValue.value === "agree" ? 3 : 2;

  if (status === 2 && (!noteText.value || noteText.value.trim() === "")) {
    ElMessage.warning("请填写驳回原因");
    return;
  }

  await handlePortfolioAuditApi({
    id: infoTableData.value.id,
    status: status,
    reason: noteText.value || ""
  });
  cancelBtn();
}
function handleTransfer() {
  rightType.value = "transfer";
}
function handleTransferSubmit(val: any) {
  // 这里处理转审逻辑
  rightType.value = "note"; // 或者根据业务回到初始
}

// PDF加载完成处理
const handlePdfLoaded = () => {
  pdfLoading.value = false;
};

// PDF加载失败处理
const handlePdfError = (error: any) => {
  pdfLoading.value = false;
  ElMessage.error("PDF加载失败，请稍后重试");
};

// 附件预览处理
const handlePreviewAttachment = () => {
  if (attachmentUrl.value) {
    // 在新窗口中打开附件，添加窗口特性参数
    const newWindow = window.open(
      attachmentUrl.value,
      "_blank",
      "noopener,noreferrer"
    );
    if (newWindow) {
      newWindow.focus();
    }
  } else {
    ElMessage.warning("暂无附件可预览");
  }
};

// 审核记录选择
const transferList = [
  { name: "A组张三", value: "zhangsan" },
  { name: "A组李四", value: "lisi" }
];
const transferValue = ref("lisi");

// 工具函数：处理PDF代理地址
function getProxyPdfUrl(url: string) {
  if (!url) return "";
  return url.includes("img-test.easyzhipin.com")
    ? url.replace("https://img-test.easyzhipin.com", "/api/pdf")
    : url;
}

// 工具函数：计算审核状态
function getAuditStatus(row: any) {
  // 兼容不同字段
  const status = row?.manualInspectionStatus ?? row?.status;
  if (status === 3) return "1";
  if (status === 2) return "2";
  return "0";
}

watch(
  () => $props.currentRow,
  newVal => {
    // 重置PDF状态
    pdfLoading.value = false;

    if (!newVal) {
      auditList.value = [];
      statusList.value = [];
      infoTableData.value = {};
      avatarUrl.value = "";
      attachmentUrl.value = "";
      return;
    }

    auditList.value = [
      {
        auditUserName: newVal.manualInspectionUserName || "",
        auditTime: newVal.manualInspectionTime || "",
        status: getAuditStatus(newVal),
        reason: newVal.manualInspectionReason || newVal.reason || ""
      }
    ];
    statusList.value = []; // 如有需要可补充

    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl || "";
    const pdfUrl = getProxyPdfUrl(newVal.attachmentUrl);
    attachmentUrl.value = pdfUrl;

    // 如果有PDF URL，设置加载状态
    if (pdfUrl) {
      pdfLoading.value = true;
    }
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 顶部信息卡片 -->
      <el-table
        :data="[infoTableData]"
        border
        class="info-table info-table-top"
        style="width: 100%; margin-bottom: 32px"
        :show-header="true"
        :header-cell-style="{
          background: '#fff',
          color: '#888',
          fontWeight: 500,
          fontSize: '16px',
          textAlign: 'center'
        }"
        :cell-style="{
          background: '#fff',
          color: '#222',
          fontSize: '18px',
          fontWeight: 500,
          textAlign: 'center'
        }"
      >
        <el-table-column
          prop="id"
          label="提交人ID"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="scope">
            <span>ID：{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="提交人姓名" min-width="100" />
        <el-table-column prop="phone" label="提交人电话" min-width="140" />
        <el-table-column prop="createTime" label="提交时间" min-width="180">
          <template #default="scope">
            <span>{{ formatTimestamp(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- 下方内容区 -->
      <div class="content-row">
        <!-- 左侧证书图片 -->
        <div class="card-box attachment-box">
          <div class="card-title">附件</div>
          <div class="pdf-preview-wrapper">
            <!-- PDF加载动画 -->
            <div v-if="pdfLoading && attachmentUrl" class="pdf-loading">
              <div class="loading-spinner" />
              <div class="loading-text">PDF加载中...</div>
            </div>
            <!-- PDF内容 -->
            <VuePdfEmbed
              v-if="attachmentUrl"
              :source="attachmentUrl"
              class="pdf-preview"
              :style="{ display: pdfLoading ? 'none' : 'block' }"
              @loaded="handlePdfLoaded"
              @loading-failed="handlePdfError"
            />
            <!-- 无附件提示 -->
            <div v-if="!attachmentUrl" class="no-pdf">暂无附件</div>
          </div>
          <!-- 附件预览按钮 -->
          <div v-if="attachmentUrl" class="attachment-preview-btn">
            <el-button type="primary" @click="handlePreviewAttachment">
              附件预览
            </el-button>
          </div>
        </div>
        <div class="card-box">
          <template v-if="rightType === 'transfer'">
            <TransferUserSelect
              v-model="transferValue"
              :transferList="transferList"
              @submit="handleTransferSubmit"
            />
          </template>
          <template v-else-if="rightType === 'record'">
            <AuditStatusRecords
              :auditList="auditList"
              :statusList="statusList"
            />
          </template>
          <template v-else>
            <ReasonEditableCard
              :options="portfolioOptions"
              v-model="noteText"
              @selectedChange="selectedValue = $event"
            />
          </template>
        </div>
      </div>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <!-- <el-button
          type="primary"
          style="background: #3477f4; border: none"
          @click="handleTransfer"
          >转审</el-button
        > -->
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 32px;
}
.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  margin-right: 24px;
}
.info-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.info-value {
  color: #222;
  font-size: 18px;
  font-weight: 500;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.attachment-box {
  .pdf-preview-wrapper {
    background: #f7f8fa;
    border-radius: 8px;
    width: 100%;
    height: 320px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  .pdf-preview {
    width: 100%;
    height: 320px;
    border: none;
    background: #f7f8fa;
  }
  .no-pdf {
    color: #888;
    font-size: 16px;
    text-align: center;
    width: 100%;
  }
  .pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #f7f8fa;
  }
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e4e7ed;
    border-top: 4px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  .loading-text {
    color: #606266;
    font-size: 14px;
    font-weight: 500;
  }
  .attachment-preview-btn {
    display: flex;
    justify-content: right;
    margin-top: 16px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
