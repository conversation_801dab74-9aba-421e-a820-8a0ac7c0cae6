import { http } from "@/utils/http";

// 查询岗位列表
const getPositionList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/positionInfo/queryList", {
    data
  });
};

// 岗位信息审核
const handlePositionAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/positionInfo/updateById", {
    data
  });
};

// 公司信息查询
const getCompanyInfo = (data: any) => {
  return http.request("post", "/easyzhipin-admin/company/queryById", {
    data
  });
};

// 更新公司信息
const handleCompanyInfoUpdate = (data: any) => {
  return http.request("post", "/easyzhipin-admin/company/update", {
    data
  });
}

// 个人信息查看
const getPersonalInfo = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/queryByBaseInfoId",
    {
      data
    }
  );
};

// 查询用户简历接口
const getResumeList = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/queryList", {
    data
  });
};

// 个人亮点，技能，求职状态修改接口
const handleResumeAudit = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/updateById", {
    data
  });
};

// 求职期望查看接口
const getJobExpectations = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/queryUserJobIntention",
    {
      data
    }
  );
};

// 求职期望修改接口
const handleJobExpectations = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/updateUserJobIntentionById",
    {
      data
    }
  );
};

// 教育经历查看接口
const getEducation = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/queryEducationList",
    {
      data
    }
  );
};
// 教育经历修改接口
const handleEducation = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/updateByEducationId",
    {
      data
    }
  );
};

// 工作经历查看接口
const getWorkExperience = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/queryWorkExperience",
    {
      data
    }
  );
};

// 工作经历修改接口
const handleWorkExperience = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/updateWorkExperienceId",
    {
      data
    }
  );
};

// 项目经历
const getProjectExperience = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/queryProject", {
    data
  });
};
// 项目经历修改接口
const handleProjectExperience = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/updateProjectId", {
    data
  });
};

// 资格证书
const getCertifications = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/queryCertificate", {
    data
  });
};

// 资格证书修改接口
const handleCertifications = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/updateCertificateId",
    {
      data
    }
  );
};

// 附件/作品集
const getAttachmentSet = (data: any) => {
  return http.request("post", "/easyzhipin-admin/userResume/queryResumeFile", {
    data
  });
};

// 附件/作品集修改接口
const handleAttachmentSet = (data: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/userResume/updateResumeFileId",
    {
      data
    }
  );
};

export {
  getPositionList,
  getPersonalInfo,
  handlePositionAudit,
  getResumeList,
  handleResumeAudit,
  getJobExpectations,
  handleJobExpectations,
  getEducation,
  handleEducation,
  getWorkExperience,
  handleWorkExperience,
  getProjectExperience,
  handleProjectExperience,
  getCertifications,
  handleCertifications,
  getAttachmentSet,
  handleAttachmentSet,
  getCompanyInfo,
  handleCompanyInfoUpdate
};
