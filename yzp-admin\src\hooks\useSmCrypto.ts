import { sm4 } from "sm-crypto";

// 获取环境变量中的SM4密钥
const getSM4Key = (): string => {
  // 从环境变量获取密钥，开发环境使用.env，生产环境使用.env.production
  const envKey = import.meta.env.VITE_SM4_KEY;

  if (!envKey) {
    console.warn("未找到VITE_SM4_KEY环境变量，使用默认密钥");
    return "9852c96d077b16899a7fc6d0d1609ca0"; // 默认密钥
  }

  return envKey;
};

// sm4 加密
export function encryption(params) {
  const key = getSM4Key();
  return sm4.encrypt(params, key);
}

// sm4 解密
export function decryption(params) {
  const key = getSM4Key();
  return sm4.decrypt(params, key);
}
