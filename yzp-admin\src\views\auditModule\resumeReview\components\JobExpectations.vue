<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  getJobExpectations,
  handleJobExpectations
} from "@/api/auditModule/index";
import rawCity from "@/utils/city.json";
import rawIndustry from "@/utils/industry.json";
import rawPosition from "@/utils/position.json";
import { SALARY_RANGE } from "@/enum/salaryRange";

// 递归转换 childList 为 children
function convertCityData(list: any[]) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children: item.childList ? convertCityData(item.childList) : undefined
    };
    delete newItem.childList;
    return newItem;
  });
}

// 这里要用 rawCity.zpData.cityList
const city = ref<any[]>([]);
const industryTree = ref<any[]>([]);

let cityLoaded = false;
let industryLoaded = false;
let positionLoaded = false;

const cityLoading = ref(false);
const industryLoading = ref(false);
const positionLoading = ref(false);

async function loadCity() {
  if (!cityLoaded) {
    cityLoading.value = true;
    const rawCity = await import("@/utils/city.json");
    city.value = convertCityData(rawCity.default.zpData.cityList);
    cityLoaded = true;
    cityLoading.value = false;
  }
}
async function loadIndustry() {
  if (!industryLoaded) {
    industryLoading.value = true;
    const rawIndustry = await import("@/utils/industry.json");
    industryTree.value = convertIndustryData(rawIndustry.default.industryData);
    industryLoaded = true;
    industryLoading.value = false;
  }
}
async function loadPosition() {
  if (!positionLoaded) {
    positionLoading.value = true;
    const rawPosition = await import("@/utils/position.json");
    positionTree.value = convertPositionData(
      rawPosition.default.zpData.position
    );
    positionLoaded = true;
    positionLoading.value = false;
  }
}

const loading = ref<boolean>(false);
const infoTableData = ref<any>({});

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentId: {
    type: Number,
    default: null
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 监听弹框显示状态变化，只在弹框打开时加载数据
watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal && $props.currentRow?.userId) {
      loadDataAndExpectations();
    }
  },
  { immediate: false }
);

// 加载数据并获取求职期望
async function loadDataAndExpectations() {
  try {
    // 并行加载基础数据
    await Promise.all([loadCity(), loadIndustry(), loadPosition()]);

    // 基础数据加载完成后，再获取求职期望数据
    await getJobExpectationsData();
  } catch (error) {
    console.error("加载数据失败:", error);
  }
}

const formRefs: any[] = [];
const rules = {
  jobType: [{ required: true, message: "请选择求职类型", trigger: "change" }],
  workLocation: [
    { required: true, message: "请选择工作地点", trigger: "change" }
  ],
  jobName: [{ required: true, message: "请选择期望岗位", trigger: "change" }],
  industry: [{ required: true, message: "请选择期望行业", trigger: "change" }],
  salaryRange: [
    { required: true, message: "请选择薪资范围", trigger: "change" }
  ]
};

const jobTypeList = ref<any[]>([
  { label: "全职", value: 1 },
  { label: "兼职", value: 2 },
  { label: "实习", value: 3 }
]);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}
// 处理薪资范围回显
const handleSalaryRange = (
  salaryExpectationStart: any,
  salaryExpectationEnd: any
) => {
  if (salaryExpectationStart === 0 && salaryExpectationEnd === 0) {
    return (infoTableData.value.salaryPath = ["面议", "面议"]);
  } else {
    let salaryStart = salaryExpectationStart && salaryExpectationStart / 1000;
    let salaryEnd = salaryExpectationEnd && salaryExpectationEnd / 1000;
    return (infoTableData.value.salaryRange = [
      `${salaryStart}k`,
      `${salaryEnd}k`
    ]);
  }
};

const getJobExpectationsData = async () => {
  const res: any = await getJobExpectations({
    userId: $props.currentRow?.userId
  });
  if (res.code === 0) {
    if (Array.isArray(res.data) && res.data.length > 0) {
      infoTableData.value = res.data.slice(0, 3).map(item => {
        // 处理薪资范围
        const salaryRangeData = handleSalaryRange(
          item.salaryExpectationStart,
          item.salaryExpectationEnd
        );

        const industryPath = findIndustryPathByCode(
          industryTree.value,
          item.expectedIndustryCode
        );
        console.log("🚀 ~ 行业回显数据:", industryPath);

        // 处理岗位路径
        const jobNamePath = findPositionPathByCode(
          positionTree.value,
          item.expectedPositionsCode
        );

        return {
          ...item,
          // v-model 绑定的路径数组
          salaryRange: salaryRangeData,
          industry: industryPath,
          jobName: jobNamePath,
          workLocation: [item.provinceCode, item.cityCode, item.districtCode],

          // 详细信息用于提交
          salaryRangeDetail: salaryRangeData,
          industryDetail: item.expectedIndustryCode
            ? {
                code: item.expectedIndustryCode,
                name: item.expectedIndustry || ""
              }
            : null,
          jobNameDetail: item.expectedPositionsCode
            ? {
                code: item.expectedPositionsCode,
                name: item.expectedPositions || ""
              }
            : null
        };
      });
    } else {
      infoTableData.value = [
        {
          id: null,
          jobType: "",
          workLocation: [],
          jobName: null,
          industry: null,
          salaryRange: null
        }
      ];
    }
  }
};

// 确认
const handlePass = async (id: number, index: number) => {
  const form = formRefs[index];
  if (!form) return;
  form.validate(async (valid: boolean) => {
    if (!valid) return;
    infoTableData.value[index].id = id;

    // 构造提交数据，转换为后端期望的格式
    const currentData = infoTableData.value[index];
    const submitData = {
      industry: "1",
      id: id,
      jobType: currentData.jobType,
      // 工作地点相关字段
      provinceCode: currentData.provinceCode || "",
      provinceName: currentData.provinceName || "",
      cityCode: currentData.cityCode || "",
      cityName: currentData.cityName || "",
      districtCode: currentData.districtCode || "",
      districtName: currentData.districtName || "",
      // 期望岗位
      expectedPositionsCode: currentData.jobNameDetail?.code || "",
      expectedPositions: currentData.jobNameDetail?.name || "",
      // 期望行业
      expectedIndustryCode: currentData.industryDetail?.code || "",
      expectedIndustry: currentData.industryDetail?.name || "",
      // 薪资范围
      salaryExpectationStart:
        currentData.salaryRangeDetail?.begin === "面议"
          ? 0
          : currentData.salaryRangeDetail?.begin
            ? parseInt(currentData.salaryRangeDetail.begin.replace("k", "")) *
              1000
            : 0,
      salaryExpectationEnd:
        currentData.salaryRangeDetail?.end === "面议"
          ? 0
          : currentData.salaryRangeDetail?.end
            ? parseInt(currentData.salaryRangeDetail.end.replace("k", "")) *
              1000
            : 0
    };

    const res: any = await handleJobExpectations(submitData);
    if (res.code === 0) {
      ElMessage.success(`求职期望${index + 1}修改成功`);
      cancelBtn();
      $emit("update:updataList", true);
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  });
};

// 取消
function handleReject() {
  cancelBtn();
}

const cascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: true // 允许选择市或区
};

// 选中后获取省市区 code 和 name
function findNodesByCodes(codes: string[], nodes: any[]): any[] {
  let result: any[] = [];
  let currentNodes = nodes;
  for (const code of codes) {
    const node = currentNodes.find((n: any) => n.code === code);
    if (node) {
      result.push(node);
      currentNodes = node.children || [];
    } else {
      break;
    }
  }
  return result;
}

// 根据 code 递归查找节点
function findNodeByCode(nodes: any[], code: string | number): any {
  for (const node of nodes) {
    if (String(node.code) === String(code)) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByCode(node.children, code);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

function handleLocationChange(val: string[], index: number) {
  const selectedData = findNodesByCodes(val, city.value);
  if (selectedData.length === 1) {
    // 只选了省，不允许
    ElMessage.warning("请选择市或区");
    return;
  }
  if (selectedData.length === 2 || selectedData.length === 3) {
    // 选了市或区
    infoTableData.value[index].provinceCode = selectedData[0]?.code || "";
    infoTableData.value[index].provinceName = selectedData[0]?.name || "";
    infoTableData.value[index].cityCode = selectedData[1]?.code || "";
    infoTableData.value[index].cityName = selectedData[1]?.name || "";
    infoTableData.value[index].districtCode = selectedData[2]?.code || "";
    infoTableData.value[index].districtName = selectedData[2]?.name || "";
  } else {
    infoTableData.value[index].provinceCode = "";
    infoTableData.value[index].provinceName = "";
    infoTableData.value[index].cityCode = "";
    infoTableData.value[index].cityName = "";
    infoTableData.value[index].districtCode = "";
    infoTableData.value[index].districtName = "";
  }
}

// 递归拍平所有叶子岗位
function flattenPositions(list: any[]): any[] {
  let result: any[] = [];
  list.forEach(item => {
    if (item.subLevelModelList && item.subLevelModelList.length > 0) {
      result = result.concat(flattenPositions(item.subLevelModelList));
    } else if (item.name && item.code) {
      result.push({ code: item.code, name: item.name });
    }
  });
  return result;
}

// 取出所有叶子岗位
const positionList = ref<any[]>(flattenPositions(rawPosition.zpData.position));

function handleJobNameChange(code: string | number) {
  const selected = positionList.value.find(item => item.code === code);
  if (selected) {
    infoTableData.value.jobName = { code: selected.code, name: selected.name };
  } else {
    infoTableData.value.jobName = null;
  }
}

// 递归转换 subLevelModelList 为 children，只到第三级
function convertPositionData(list: any[], depth = 1) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children:
        item.subLevelModelList && depth < 3
          ? convertPositionData(item.subLevelModelList, depth + 1)
          : undefined
    };
    delete newItem.subLevelModelList;
    return newItem;
  });
}

// 取出树形结构，只到第三级
const positionTree = ref<any[]>(
  convertPositionData(rawPosition.zpData.position, 1)
);

const positionCascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: false // 只能选到叶子
};

// 选中后获取岗位 code 和 name
function handlePositionChange(val: (string | number)[], index: number) {
  if (val && val.length > 0) {
    const lastCode = val[val.length - 1];
    const selectedNode = findNodeByCode(positionTree.value, lastCode);

    if (selectedNode) {
      // v-model 绑定路径数组（用于显示）
      infoTableData.value[index].jobName = val;
      // 保存选中节点的详细信息（用于提交）
      infoTableData.value[index].jobNameDetail = {
        code: selectedNode.code,
        name: selectedNode.name
      };
    } else {
      infoTableData.value[index].jobName = null;
      infoTableData.value[index].jobNameDetail = null;
    }
  } else {
    infoTableData.value[index].jobName = null;
    infoTableData.value[index].jobNameDetail = null;
  }
}

// 递归转换 childerIndustryData 为 children
function convertIndustryData(list: any[]) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children: item.childerIndustryData
        ? convertIndustryData(item.childerIndustryData)
        : undefined
    };
    delete newItem.childerIndustryData;
    return newItem;
  });
}

const industryCascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: false // 只能选到叶子
};

// 选中后获取行业 code 和 name
function handleIndustryChange(val: (string | number)[], index: number) {
  if (val && val.length > 0) {
    // 获取最后一级的 code
    const lastCode = val[val.length - 1];

    // 根据 code 在 industryTree 中查找对应的节点信息
    const selectedNode = findNodeByCode(industryTree.value, lastCode);

    if (selectedNode) {
      // v-model 绑定路径数组（用于显示）
      infoTableData.value[index].industry = val;
      // 保存选中节点的详细信息（用于提交）
      infoTableData.value[index].industryDetail = {
        code: selectedNode.code,
        name: selectedNode.name
      };
    } else {
      infoTableData.value[index].industry = null;
      infoTableData.value[index].industryDetail = null;
    }
  } else {
    infoTableData.value[index].industry = null;
    infoTableData.value[index].industryDetail = null;
  }
}

function salaryRangeToOptions(rangeObj: Record<string, string[]>): any[] {
  return Object.entries(rangeObj).map(([start, ends]) => ({
    value: start,
    label: start,
    children: ends.map(end => ({
      value: end,
      label: end
    }))
  }));
}

const salaryOptions = ref<any[]>(salaryRangeToOptions(SALARY_RANGE));

const salaryCascaderProps = {
  label: "label",
  value: "value",
  children: "children",
  emitPath: true,
  checkStrictly: false,
  expandTrigger: "hover"
};

function handleSalaryChange(val: string[], index: number) {
  if (val && val.length === 2) {
    // v-model 绑定路径数组（用于显示）
    infoTableData.value[index].salaryRange = val;
    // 保存薪资详细信息（用于提交）
    infoTableData.value[index].salaryRangeDetail = {
      begin: val[0],
      end: val[1]
    };
  } else if (val && val.length === 1 && val[0] === "面议") {
    infoTableData.value[index].salaryRange = val;
    infoTableData.value[index].salaryRangeDetail = {
      begin: "面议",
      end: "面议"
    };
  } else {
    infoTableData.value[index].salaryRange = null;
    infoTableData.value[index].salaryRangeDetail = null;
  }
}

// 自定义显示薪资范围格式
function displaySalaryLabel(labels: string[]) {
  // 始终显示所有级联（一级和二级），如 1k - 5k，或面议
  if (labels.length === 1 && labels[0] === "面议") {
    return "面议";
  }
  if (labels.length === 2) {
    return `${labels[0]} - ${labels[1]}`;
  }
  return labels.join(" - ");
}

watch(
  () => infoTableData.value.jobName,
  val => {
    if (val && val.code) {
      infoTableData.value.jobNameCode = val.code;
    }
  },
  { immediate: true }
);

// 递归查找行业path
function findIndustryPathByCode(tree, code, path = []) {
  if (!tree || !code) return [];

  for (const node of tree) {
    const currentPath = [...path, node.code];
    // 使用字符串比较，确保类型一致
    if (String(node.code) === String(code)) {
      return currentPath;
    }
    if (node.children) {
      const childPath = findIndustryPathByCode(
        node.children,
        code,
        currentPath
      );
      if (childPath.length) {
        return childPath;
      }
    }
  }
  return [];
}

// 递归查找岗位path
function findPositionPathByCode(tree, code, path = []) {
  const targetCode = String(code);
  for (const node of tree) {
    const currentPath = [...path, node.code];
    if (String(node.code) === targetCode) {
      return currentPath;
    }
    if (node.children && node.children.length) {
      const childPath = findPositionPathByCode(
        node.children,
        code,
        currentPath
      );
      if (childPath && childPath.length) {
        return childPath;
      }
    }
  }
  return [];
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <div v-for="(item, index) in infoTableData" :key="item.id || index">
        <div>
          <div
            style="
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 20px;
              text-align: left;
            "
          >
            {{ `求职期望${index + 1}` }}
          </div>
          <el-form
            :ref="el => (formRefs[index] = el)"
            :model="item"
            label-width="100px"
            :rules="rules"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="求职类型" prop="jobType" required>
                  <el-select
                    v-model="item.jobType"
                    placeholder="请选择求职类型"
                  >
                    <el-option
                      v-for="item in jobTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作地点" prop="workLocation" required>
                  <el-cascader
                    v-model="item.workLocation"
                    :options="city"
                    :props="cascaderProps"
                    :loading="cityLoading"
                    placeholder="请选择工作地点"
                    style="width: 100%"
                    @change="val => handleLocationChange(val, index)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="期望岗位" prop="jobName" required>
                  <el-cascader
                    v-model="item.jobName"
                    :options="positionTree"
                    :props="positionCascaderProps"
                    :loading="positionLoading"
                    placeholder="请选择期望岗位"
                    style="width: 100%"
                    filterable
                    clearable
                    :show-all-levels="false"
                    @change="val => handlePositionChange(val, index)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="期望行业" prop="industry" required>
                  <el-cascader
                    v-model="item.industry"
                    :options="industryTree"
                    :props="industryCascaderProps"
                    :loading="industryLoading"
                    placeholder="请选择期望行业"
                    style="width: 100%"
                    filterable
                    clearable
                    :show-all-levels="false"
                    @change="val => handleIndustryChange(val, index)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="薪资范围" prop="salaryRange" required>
                  <el-cascader
                    v-model="item.salaryRange"
                    :options="salaryOptions"
                    :props="salaryCascaderProps"
                    placeholder="请选择薪资范围"
                    style="width: 100%"
                    filterable
                    clearable
                    :show-all-levels="true"
                    :display-render="displaySalaryLabel"
                    @change="val => handleSalaryChange(val, index)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- 按钮组 -->
        <div class="btn-group-bottom">
          <el-button @click="() => handleReject()">取消</el-button>
          <el-button type="success" @click="() => handlePass(item.id, index)"
            >确认</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
