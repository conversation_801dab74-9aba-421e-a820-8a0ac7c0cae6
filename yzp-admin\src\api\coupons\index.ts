import { http } from "@/utils/http";

// 获取道具卡列表
const getCouponsList = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/categoryAreaPrice/queryList",
    { data },
    config
  );
};

// 获取道具类别
const getPropCategory = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/categoryAreaPrice/categoryQueryList",
    { data },
    config
  );
};

// 新增
const addCoupons = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/categoryAreaPrice/add",
    { data },
    config
  );
};

// 修改
const updateCoupons = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/categoryAreaPrice/update",
    { data },
    config
  );
};

// 删除
const deleteCoupons = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/categoryAreaPrice/deleteById",
    { data },
    config
  );
};

// 道具列表查询
const getItemCardsList = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/category/queryList",
    { data },
    config
  );
};

// 道具卡修改
const updateItemCards = (data?: any, config?: any) => {
  return http.request(
    "post",
    "/easyzhipin-admin/category/update",
    { data },
    config
  );
};
export {
  getCouponsList,
  getPropCategory,
  addCoupons,
  updateCoupons,
  deleteCoupons,
  getItemCardsList,
  updateItemCards
};
