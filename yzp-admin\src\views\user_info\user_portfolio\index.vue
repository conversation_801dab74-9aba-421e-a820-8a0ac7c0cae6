<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { customList } from "@/utils/custom";
import TableDetails from "./components/index.vue";
import type { ComponentSize } from "element-plus";
import { getPortfolioList } from "@/api/user_section";
import { useRoute } from "vue-router";
import { formatTimestamp } from "@/utils/dateFormat";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const isRightType = ref("");
const total = ref(0);
const businessStatus = ref(0);
const route = useRoute();
const currentRow = ref({});
const closeOnClickModal = ref(false);

const params = ref({
  entity: {
    endTime: "",
    name: "",
    phone: "",
    startTime: "",
    status: null
  },
  orderBy: {},
  page: 1,
  size: 10
});

let formQuery = [
  {
    type: "input",
    key: "name",
    label: "姓名"
  },
  {
    type: "input",
    key: "phone",
    label: "手机号"
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];
interface User {
  date: string;
  name: string;
  address: string;
}
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "name",
    label: "姓名",
    width: ""
  },
  {
    property: "age",
    label: "年龄",
    width: ""
  },
  {
    property: "sex",
    label: "性别",
    width: ""
  },
  {
    property: "phone",
    label: "手机号码",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  }
];
const tableData = ref([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getPortfolioListData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getPortfolioListData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

// 获取作品集列表
const getPortfolioListData = async () => {
  const res: any = await getPortfolioList({ ...params.value });
  if (res.code === 0) {
    tableData.value = res.data.list;
    total.value = res.data.total;
  }
};

// 搜索
const handleSearch = () => {
  // 只处理用户输入的字段
  if (form.value.dates && form.value.dates.length === 2) {
    params.value.entity.startTime = form.value.dates[0];
    params.value.entity.endTime = form.value.dates[1];
  } else {
    delete params.value.entity.startTime;
    delete params.value.entity.endTime;
  }
  params.value.entity.name = form.value.name || undefined;
  params.value.entity.phone = form.value.phone || undefined;
  // status 不变
  getPortfolioListData();
};

// 重置
const handleReset = () => {
  form.value = {};
  params.value.entity = {
    endTime: "",
    name: "",
    phone: "",
    startTime: "",
    status: businessStatus.value
  };
  getPortfolioListData();
};

// 审批
const handleApproval = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "note";
};

// 转办
const handleTransfer = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "transfer";
};

// 操作记录
const handleRecord = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = true;
  isRightType.value = "record";
};

const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  closeOnClickModal.value = false;
  getPortfolioListData();
};

onMounted(() => {
  const statusParam = route.meta.businessStatus;
  params.value.entity.status =
    statusParam !== undefined ? Number(statusParam) : 0;
  businessStatus.value = statusParam !== undefined ? Number(statusParam) : 0;
  getPortfolioListData();
  customList.forEach(res => {
    console.log(res);
  });
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              :placeholder="'请输入' + item.label"
              style="min-width: 170px"
              clearable
            />
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        style="width: 100%"
        border
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) of tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          :property="config.property"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="config.property === 'createTime'">
              {{ formatTimestamp(scope.row[config.property]) }}
            </span>

            <span v-else-if="config.property === 'sex'">
              {{ scope.row[config.property] === 1 ? "男" : "女" }}
            </span>

            <span v-else>
              {{ scope.row[config.property] }}
            </span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" min-width="60">
          <template #default="scope">
            <el-button
              v-if="businessStatus === 1"
              link
              type="primary"
              style="color: #279efb"
              @click="handleApproval(scope)"
            >
              视检
            </el-button>
            <!-- <el-button
              v-if="businessStatus === 1"
              link
              type="primary"
              style="color: #fb2727"
              @click="handleTransfer(scope)"
            >
              转办
            </el-button> -->
            <el-button
              v-if="businessStatus === 2 || businessStatus === 3"
              link
              type="primary"
              style="color: #4eb906"
              @click="handleRecord(scope)"
            >
              操作记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :isRightType="isRightType"
      :currentRow="currentRow"
      :closeOnClickModal="closeOnClickModal"
      @cancelBtn="handleCancelBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-top: 12px;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
