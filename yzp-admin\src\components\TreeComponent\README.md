# TreeComponent 树形组件

一个基于 Element Plus 的可复用树形组件，支持搜索、自定义配置等功能。

## 功能特性

- ✅ 支持搜索功能（可配置是否显示）
- ✅ 支持自定义搜索占位符
- ✅ 支持节点点击、展开、收起事件
- ✅ 支持高亮当前选中节点
- ✅ 支持默认展开所有节点
- ✅ 支持自定义树形数据结构
- ✅ 完整的 TypeScript 类型支持

## 基本用法

```vue
<template>
  <TreeComponent
    :tree-data="treeData"
    :show-search="true"
    search-placeholder="搜索分类"
    @node-click="handleTreeNodeClick"
  />
</template>

<script setup lang="ts">
import TreeComponent from '@/components/TreeComponent/index.vue'

const treeData = ref([
  {
    id: 1,
    label: '一级节点',
    children: [
      {
        id: 11,
        label: '二级节点1'
      },
      {
        id: 12,
        label: '二级节点2'
      }
    ]
  }
])

const handleTreeNodeClick = (data, node, instance) => {
  console.log('选中的节点:', data)
}
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| treeData | 树形数据 | TreeNode[] | [] |
| showSearch | 是否显示搜索框 | boolean | true |
| searchPlaceholder | 搜索框占位符 | string | '搜索' |
| props | 树形数据配置 | TreeProps | { children: 'children', label: 'label', disabled: 'disabled' } |
| defaultExpandAll | 是否默认展开所有节点 | boolean | false |
| expandOnClickNode | 是否在点击节点时展开/收起节点 | boolean | false |
| highlightCurrent | 是否高亮当前选中节点 | boolean | true |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| node-click | 节点被点击时触发 | (data, node, instance) |
| node-expand | 节点被展开时触发 | (data, node, instance) |
| node-collapse | 节点被收起时触发 | (data, node, instance) |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| filter | 过滤节点 | (value: string) |
| setCurrentKey | 设置当前选中节点 | (key: string \| number) |
| getCurrentKey | 获取当前选中节点的 key | - |
| getCurrentNode | 获取当前选中节点的数据 | - |
| setCheckedKeys | 设置选中的节点 | (keys: (string \| number)[]) |
| getCheckedKeys | 获取选中节点的 key 数组 | - |
| setCheckedNodes | 设置选中的节点 | (nodes: TreeNode[]) |
| getCheckedNodes | 获取选中节点的数据数组 | - |

### TreeNode 数据结构

```typescript
interface TreeNode {
  id: string | number
  label: string
  children?: TreeNode[]
  [key: string]: any
}
```

## 样式定制

组件使用了 scoped 样式，如需自定义样式，可以通过以下 CSS 变量进行调整：

```css
.tree-component {
  --tree-node-height: 36px;
  --tree-hover-bg: #f5f7fa;
  --tree-current-bg: #e6f7ff;
  --tree-current-color: #1890ff;
}
```
