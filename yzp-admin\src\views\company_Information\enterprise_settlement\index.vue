<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import TableDetails from "./components/index.vue";
import { getCompanyHrList } from "@/api/company/index";
import { useRoute } from "vue-router";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const isRightType = ref("");
const businessStatus = ref(0);
const loading = ref(false);
const closeOnClickModal = ref(false);

let formQuery = [
  // {
  //   type: "input",
  //   key: "enterpriseLegalPerson",
  //   label: "法人"
  // },
  {
    type: "input",
    key: "hrCardName",
    label: "招聘者"
  },
  {
    type: "input",
    key: "name",
    label: "公司名称"
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];
interface User {
  date: string;
  name: string;
  address: string;
}
interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "name",
    label: "公司名称",
    width: ""
  },
  {
    property: "enterpriseLegalPerson",
    label: "法人",
    width: ""
  },
  {
    property: "socialCreditCode",
    label: "社会信用代码",
    width: ""
  },
  {
    property: "hrCardName",
    label: "招聘者",
    width: ""
  },
  {
    property: "phone",
    label: "电话",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  }
];
const tableData = ref<any[]>([]);
import type { ComponentSize } from "element-plus";

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});

const route = useRoute();

// 分页接口入参
const params = ref<any>({
  entity: {
    cityCode: "",
    cityName: "",
    companyId: null,
    districtCode: "",
    districtName: "",
    provideCode: "",
    provideName: "",
    status: null,
    type: null
  },
  orderBy: {},
  page: 1,
  size: 10
});

const getCompanyHrListData = async () => {
  loading.value = true;
  try {
    const res: any = await getCompanyHrList(params.value);
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getCompanyHrListData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getCompanyHrListData();
};
const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

function handleApproval(item) {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "note";
}
const handleTransfer = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "transfer";
};
const handleRecord = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = true;
  isRightType.value = "record";
};
const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  closeOnClickModal.value = false;
  getCompanyHrListData();
};

// 搜索
const handleSearch = () => {
  // 只处理用户输入的字段
  if (form.value.dates && form.value.dates.length === 2) {
    params.value.entity.startTime = form.value.dates[0];
    params.value.entity.endTime = form.value.dates[1];
  } else {
    delete params.value.entity.startTime;
    delete params.value.entity.endTime;
  }
  params.value.entity.name = form.value.name || undefined;
  params.value.entity.hrCardName = form.value.hrCardName || undefined;
  params.value.entity.enterpriseLegalPerson = form.value.enterpriseLegalPerson || undefined;
  // status 不变
  getCompanyHrListData();
};
// 重置
const handleReset = () => {
  form.value = {};
  params.value = {
    entity: {
      endTime: "",
      id: null,
      name: "",
      phone: "",
      reason: "",
      startTime: "",
      status: businessStatus.value
    },
    orderBy: {},
    page: 1,
    size: 10
  };
  getCompanyHrListData();
};
// 格式化时间戳函数
const formatTimestamp = (timestamp: number | string) => {
  if (!timestamp) return "";
  // 如果是字符串，尝试转换为数字
  const time = typeof timestamp === "string" ? parseInt(timestamp) : timestamp;
  // 检查是否为有效的时间戳（10位或13位）
  if (isNaN(time) || time <= 0) return "";
  // 如果是10位时间戳，转换为13位
  const milliseconds = time.toString().length === 10 ? time * 1000 : time;
  const date = new Date(milliseconds);
  // 格式化日期时间
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

onMounted(() => {
  const statusParam = route.meta.businessStatus;
  params.value.entity.status =
    statusParam !== undefined ? Number(statusParam) : 0;
  businessStatus.value = statusParam !== undefined ? Number(statusParam) : 0;
  getCompanyHrListData();
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              :placeholder="'请输入' + item.label"
              style="min-width: 170px"
              clearable
            />
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        :loading="loading"
        style="width: 100%"
        border
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) in tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="config.property === 'createTime'">
              {{ formatTimestamp(scope.row[config.property]) }}
            </span>

            <span v-else>
              {{ scope.row[config.property] }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              v-if="businessStatus === 0"
              link
              type="primary"
              style="color: #279efb"
              @click="handleApproval(scope)"
            >
              审批
            </el-button>
            <!-- <el-button
              v-if="businessStatus === 0"
              link
              type="primary"
              style="color: #fb2727"
              @click="handleTransfer(scope)"
            >
              转办
            </el-button> -->
            <el-button
              v-if="businessStatus === 1 || businessStatus === 2"
              link
              type="primary"
              style="color: #4eb906"
              @click="handleRecord(scope)"
            >
              操作记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :isRightType="isRightType"
      :currentRow="currentRow"
      :closeOnClickModal="closeOnClickModal"
      @cancelBtn="handleCancelBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-top: 12px;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
