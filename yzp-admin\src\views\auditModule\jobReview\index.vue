<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import TableDetails from "./components/index.vue";
import CompanyInfo from "./components/companyInfo.vue";
import { useRoute } from "vue-router";
import { formatTimestamp } from "@/utils/dateFormat";
import type { ComponentSize } from "element-plus";
import { getPositionList } from "@/api/auditModule/index";
import cityData from "@/utils/city.json";

const form = ref<any>({});
const detailsDialogVisible = ref(false);
const companyDialogVisible = ref(false);
const isRightType = ref("");
const businessStatus = ref(0);
const loading = ref(false);
const closeOnClickModal = ref(false);

let formQuery = [
  {
    type: "input",
    key: "positionName",
    label: "岗位名称"
  },
  {
    type: "input",
    key: "createUserName",
    label: "招聘者"
  },
  {
    type: "select",
    key: "provinceName",
    label: "省份",
    options: []
  },
  {
    type: "select",
    key: "cityName",
    label: "市",
    options: []
  },
  {
    type: "select",
    key: "districtName",
    label: "区/县",
    options: []
  },
  {
    type: "datetime",
    key: "dates",
    label: "提交时间"
  }
];

interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "positionName",
    label: "岗位名称",
    width: ""
  },
  {
    property: "positionMarkName",
    label: "岗位子标签",
    width: ""
  },
  {
    property: "workEducational",
    label: "学历要求",
    width: ""
  },
  {
    property: "workExperience",
    label: "工作经验",
    width: ""
  },
  {
    property: "workSalary",
    label: "薪资范围",
    width: ""
  },
  {
    property: "companyName",
    label: "公司名称",
    width: ""
  },
  {
    property: "createUserName",
    label: "招聘者",
    width: ""
  },
  {
    property: "provinceName",
    label: "省份",
    width: ""
  },
  {
    property: "cityName",
    label: "市",
    width: ""
  },
  {
    property: "districtName",
    label: "区/县",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  }
];
const tableData = ref<any[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});

const route = useRoute();

// 省市区数据
const provinceList = ref<any[]>([]);
const cityList = ref<any[]>([]);
const districtList = ref<any[]>([]);

// 根据省份获取城市列表
const getCityList = (provinceCode: string) => {
  const provinceName = cityData.zpData.cityList.find(
    p => p.code === provinceCode
  );
  if (provinceName && provinceName.childList) {
    cityList.value = provinceName.childList.map(cityName => ({
      code: cityName.code,
      name: cityName.name
    }));
  } else {
    cityList.value = [];
  }

  // 更新formQuery中的城市选项
  const cityItem = formQuery.find(item => item.key === "cityName");
  if (cityItem) {
    cityItem.options = cityList.value;
  }

  // 清空区和市的选择
  form.value.cityName = "";
  form.value.districtName = "";
  districtList.value = [];
};

// 根据城市获取区县列表
const getAreaList = (cityCode: string) => {
  const provinceName = cityData.zpData.cityList.find(
    p => p.code === form.value.provinceName
  );
  if (provinceName && provinceName.childList) {
    const cityName = provinceName.childList.find(c => c.code === cityCode);
    if (cityName && cityName.childList) {
      districtList.value = cityName.childList.map(districtName => ({
        code: districtName.code,
        name: districtName.name
      }));
    } else {
      districtList.value = [];
    }
  } else {
    districtList.value = [];
  }

  // 更新formQuery中的区县选项
  const areaItem = formQuery.find(item => item.key === "districtName");
  if (areaItem) {
    areaItem.options = districtList.value;
  }

  // 清空区的选择
  form.value.districtName = "";
};

// 省份变化处理
const handleProvinceChange = (provinceCode: string) => {
  getCityList(provinceCode);
};

// 城市变化处理
const handleCityChange = (cityCode: string) => {
  getAreaList(cityCode);
};

// 初始化省市区数据
const initCityData = () => {
  provinceList.value = cityData.zpData.cityList.map(provinceName => ({
    code: provinceName.code,
    name: provinceName.name
  }));

  // 更新formQuery中的省份选项
  const provinceItem = formQuery.find(item => item.key === "provinceName");
  if (provinceItem) {
    provinceItem.options = provinceList.value;
  }
};
// 分页接口入参
const params = ref<any>({
  entity: {
    createUserName: null,
    endTime: null,
    name: null,
    startTime: null,
    status: null,
    provinceCode: null,
    cityCode: null,
    districtCode: null
  },
  orderBy: {},
  page: 1,
  size: 10
});

// 处理学历要求
const handleWorkEducational = (row: any) => {
  const educationalMap: Record<number, string> = {
    0: "不限",
    1: "高中",
    2: "专科",
    3: "本科",
    4: "硕士",
    5: "博士"
  };
  return educationalMap[row.workEducational] ?? "";
};

// 处理工作经验
const handleWorkExperience = (row: any) => {
  const experienceBegin = row.workExperienceStart;
  const experienceEnd = row.workExperienceEnd;
  if (experienceBegin === 0 && experienceEnd === 0) {
    return "不限";
  }
  if (experienceBegin === 0 && experienceEnd === 1) {
    return "1年以内";
  }
  if (experienceBegin === 1 && experienceEnd === 3) {
    return "1-3年";
  }
  if (experienceBegin === 3 && experienceEnd === 5) {
    return "3-5年";
  }
  if (experienceBegin === 5 && experienceEnd === 10) {
    return "5-10年";
  }
  if (experienceBegin >= 10 || (experienceBegin && experienceEnd === 0)) {
    return "10年以上";
  }
  // 兜底
  return `${experienceBegin} - ${experienceEnd}年`;
};

// 处理薪资范围
const handleWorkSalary = (row: any) => {
  const salaryBegin = row.workSalaryBegin;
  const salaryEnd = row.workSalaryEnd;

  if (!salaryBegin && !salaryEnd) {
    return "不限";
  }

  const beginK = salaryBegin ? (salaryBegin / 1000).toFixed(0) : null;
  const endK = salaryEnd ? (salaryEnd / 1000).toFixed(0) : null;

  if (beginK && endK) {
    return `${beginK}k - ${endK}k`;
  } else {
    return "面议";
  }
};

const getJobData = async () => {
  loading.value = true;
  try {
    const res: any = await getPositionList(params.value);
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getJobData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getJobData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

function handleApproval(item) {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  companyDialogVisible.value = false;
  closeOnClickModal.value = false;
  isRightType.value = "note";
}
function handleCompany(item) {
  currentRow.value = item.row;
  detailsDialogVisible.value = false;
  companyDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "company";
}


const handleTransfer = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = false;
  isRightType.value = "transfer";
};
const handleRecord = (item: any) => {
  currentRow.value = item.row;
  detailsDialogVisible.value = true;
  closeOnClickModal.value = true;
  isRightType.value = "record";
};
const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  companyDialogVisible.value = false;
  closeOnClickModal.value = false;
  getJobData();
};

// 搜索
const handleSearch = () => {
  if (form.value.dates && form.value.dates.length === 2) {
    params.value.entity.startTime = form.value.dates[0];
    params.value.entity.endTime = form.value.dates[1];
  } else {
    delete params.value.entity.startTime;
    delete params.value.entity.endTime;
  }
  params.value.entity.createUserName = form.value.createUserName || undefined;
  params.value.entity.positionName = form.value.positionName || undefined;

  // 处理省市区字段，如果选择"全部"（空字符串）则不传该参数
  if (form.value.provinceName && form.value.provinceName !== "") {
    params.value.entity.provinceCode = form.value.provinceName;
  } else {
    delete params.value.entity.provinceCode;
  }

  if (form.value.cityName && form.value.cityName !== "") {
    params.value.entity.cityCode = form.value.cityName;
  } else {
    delete params.value.entity.cityCode;
  }

  if (form.value.districtName && form.value.districtName !== "") {
    params.value.entity.districtCode = form.value.districtName;
  } else {
    delete params.value.entity.districtCode;
  }

  // status 不变
  getJobData();
};
// 重置
const handleReset = () => {
  form.value = {
    provinceName: "",
    cityName: "",
    districtName: ""
  };
  params.value = {
    entity: {
      createUserName: null,
      endTime: null,
      positionName: null,
      startTime: null
    },
    orderBy: {},
    page: 1,
    size: 10
  };

  // 确保删除省市区字段
  delete params.value.entity.provinceCode;
  delete params.value.entity.cityCode;
  delete params.value.entity.districtCode;

  getJobData();
};

onMounted(() => {
  getJobData();
  initCityData(); // 初始化省市区数据
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              :placeholder="'请输入' + item.label"
              style="min-width: 170px"
              clearable
            />
            <el-select
              v-else-if="item.type === 'select'"
              v-model="form[item.key]"
              :placeholder="'请选择' + item.label"
              style="min-width: 170px"
              clearable
              @change="
                item.key === 'provinceName'
                  ? handleProvinceChange(form[item.key])
                  : item.key === 'cityName'
                    ? handleCityChange(form[item.key])
                    : null
              "
            >
              <!-- <el-option
                v-if="item.key === 'provinceName'"
                label="全部"
                value=""
              />
              <el-option v-if="item.key === 'cityName'" label="全部" value="" />
              <el-option
                v-if="item.key === 'districtName'"
                label="全部"
                value=""
              /> -->
              <el-option
                v-for="option in item.options"
                :key="option.code"
                :label="option.name"
                :value="option.code"
              />
            </el-select>
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        :loading="loading"
        style="width: 100%"
        border
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) in tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="config.property === 'createTime'">
              {{ formatTimestamp(scope.row[config.property]) }}
            </span>
            <span v-else-if="config.property === 'workEducational'">
              {{ handleWorkEducational(scope.row) }}
            </span>
            <span v-else-if="config.property === 'workExperience'">
              {{ handleWorkExperience(scope.row) }}
            </span>
            <span v-else-if="config.property === 'workSalary'">
              {{ handleWorkSalary(scope.row) }}
            </span>

            <span v-else>
              {{ scope.row[config.property] }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleApproval(scope)"
            >
              编辑岗位信息
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleCompany(scope)"
            >
              编辑公司
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :isRightType="isRightType"
      :currentRow="currentRow"
      :closeOnClickModal="closeOnClickModal"
      @cancelBtn="handleCancelBtn"
    />
    <CompanyInfo
      v-model:dialogVisible="companyDialogVisible"
      :currentRow="currentRow"
      :closeOnClickModal="closeOnClickModal"
      @cancelBtn="handleCancelBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-top: 12px;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
