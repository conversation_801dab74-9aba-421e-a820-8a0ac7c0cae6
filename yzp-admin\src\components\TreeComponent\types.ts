export interface TreeNode {
  code: string | number;
  name: string;
  childList?: TreeNode[];
  [key: string]: any;
}

export interface TreeProps {
  children?: string;
  label?: string;
  disabled?: string;
}

export interface TreeComponentProps {
  treeData: TreeNode[];
  showSearch?: boolean;
  searchPlaceholder?: string;
  props?: TreeProps;
  defaultExpandAll?: boolean;
  expandOnClickNode?: boolean;
  highlightCurrent?: boolean;
}

export interface TreeComponentEmits {
  (e: "node-click", data: TreeNode, node: any, instance: any): void;
  (e: "node-expand", data: TreeNode, node: any, instance: any): void;
  (e: "node-collapse", data: TreeNode, node: any, instance: any): void;
}

export interface TreeComponentExpose {
  filter: (value: string) => void;
  setCurrentKey: (key: string | number) => void;
  getCurrentKey: () => string | number | null;
  getCurrentNode: () => TreeNode | null;
  setCheckedKeys: (keys: (string | number)[]) => void;
  getCheckedKeys: () => (string | number)[];
  setCheckedNodes: (nodes: TreeNode[]) => void;
  getCheckedNodes: () => TreeNode[];
}
