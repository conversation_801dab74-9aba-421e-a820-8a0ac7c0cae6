<script lang="ts" setup>
import { ref, computed } from "vue";
import { getInfoById } from "@/api/info_query/index";
import { formatTimestamp } from "@/utils/dateFormat";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getInfoByIdData();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 获取详情
const getInfoByIdData = async () => {
  const res: any = await getInfoById({ id: $props.currentRow.id, type: 3 });
  if (res.code === 0) {
    infoTableData.value = res.data;
  } else {
    ElMessage.error(res.msg || "操作失败");
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

// 取消
async function handleClose() {
  cancelBtn();
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 详情内容区 -->
      <el-descriptions title="招聘者信息">
        <el-descriptions-item label="公司名称："
          >{{ infoTableData?.name || "暂无" }}
        </el-descriptions-item>

        <el-descriptions-item label="法人：">{{
          infoTableData?.enterpriseLegalPerson || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item label="社会信用代码：">{{
          infoTableData?.socialCreditCode || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item label="招聘者：">{{
          infoTableData?.hrCardName || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item label="职称：">{{
          infoTableData?.hrPosition || "暂无"
        }}</el-descriptions-item>

        <el-descriptions-item v-if="infoTableData?.auditStatus" label="状态：">
          <el-tag
            size="small"
            :type="
              infoTableData?.auditStatus === 0
                ? 'info'
                : infoTableData?.auditStatus === 1
                  ? 'success'
                  : 'danger'
            "
            >{{
              infoTableData?.auditStatus === 0
                ? "待审核"
                : infoTableData?.auditStatus === 1
                  ? "通过"
                  : "驳回"
            }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="提交时间：">{{
          formatTimestamp(infoTableData?.createTime)
        }}</el-descriptions-item>
      </el-descriptions>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button @click="handleClose()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}

.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
</style>
