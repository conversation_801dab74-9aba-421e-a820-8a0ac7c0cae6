<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import { updateItemCards } from "@/api/coupons/index";
import { ElMessage } from "element-plus";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const formRef = ref<any>(null);
const propCategoryList = ref<any>([]);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  isType: {
    type: String,
    default: "isAdd"
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

const form = ref<any>({
  id: "",
  categoryName: "",
  originalPrice: "",
  durationDays: "",
  price: ""
});

const rules = ref<any>({
  durationDays: [
    { required: true, message: "请输入有效期限", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const reg = /^(0|[1-9]\d*)$/; // 匹配 0 或正整数
        if (!reg.test(String(value))) {
          callback(new Error("请输入0或正整数(0为不限时)"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  originalPrice: [
    { required: true, message: "请输入原价", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正数，最多两位小数"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  price: [
    { required: true, message: "请输入实际售价", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正数，最多两位小数"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

// 获取详情
const getDetails = async () => {
  const row = $props.currentRow;
  form.value = {
    id: "",
    categoryName: row.categoryName || "",
    originalPrice: row.originalPrice / 100 || "",
    price: row.price / 100 || "",
    durationDays: row.durationDays === 0 ? "不限时" : row.durationDays
  };
};

// 取消
function cancelBtn() {
  form.value = {};
  formRef.value.resetFields();
  $emit("cancelBtn", true);
}

// 取消
async function handleClose() {
  cancelBtn();
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const payload = {
      ...form.value,
      durationDays: Number(form.value.durationDays) || 0,
      originalPrice: Math.round(Number(form.value.originalPrice) * 100),
      price: Math.round(Number(form.value.price) * 100)
    };
    const res: any = await updateItemCards({
      ...payload,
      id: $props.currentRow.id
    });
    if (res.code === 0) {
      ElMessage.success("操作成功");
      cancelBtn();
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  } catch (err) {
    console.warn("表单校验未通过:", err);
  }
};

watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal) {
      if ($props.isType === "isEdit" || $props.isType === "isDetails") {
        getDetails();
      }
    }
  },
  { immediate: true } // 如果有可能组件一加载就是 true，可以立即执行
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="isType === 'isDetails' ? '详情' : '编辑'"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 详情内容区 -->
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        require-asterisk-position="right"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              label="道具卡名称："
              prop="categoryName"
              label-position="right"
            >
              <el-input
                v-model="form.categoryName"
                placeholder="请输入道具卡名称"
                :disabled="isType === 'isDetails' || isType === 'isEdit'"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="有效期限："
              prop="durationDays"
              label-position="right"
            >
              <el-input
                v-model="form.durationDays"
                placeholder="请输入有效期限（0为不限时）"
                :disabled="isType === 'isDetails'"
                style="width: 80%"
              >
                <template #append>天</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              label="原价："
              prop="originalPrice"
              label-position="right"
            >
              <el-input
                v-model="form.originalPrice"
                placeholder="请输入原价"
                :disabled="isType === 'isDetails'"
                style="width: 80%"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="实际售价："
              prop="price"
              label-position="right"
            >
              <el-input
                v-model="form.price"
                placeholder="请输入实际售价"
                :disabled="isType === 'isDetails'"
                style="width: 80%"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}

.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.job-desc {
  margin-bottom: 5px !important;
  margin-top: 10px !important;
}
</style>
