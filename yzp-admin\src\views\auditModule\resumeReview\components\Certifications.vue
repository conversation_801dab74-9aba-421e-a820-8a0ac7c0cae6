<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  getCertifications,
  handleCertifications
} from "@/api/auditModule/index";
import { Edit, Check, Close } from "@element-plus/icons-vue";

const loading = ref<boolean>(false);
// infoTableData为数组
const infoTableData = ref<any[]>([]);

// 当前编辑的证书索引
const currentEditIndex = ref<number | null>(null);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentId: {
    type: Number,
    default: null
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 监听弹框显示状态变化，只在弹框打开时加载数据
watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal && $props.currentId) {
      getCertificationsData();
    }
  },
  { immediate: false }
);

const rules = {
  certificate: [{ required: true, message: "请输入证书名称", trigger: "blur" }]
};

const getCertificationsData = async () => {
  const res: any = await getCertifications({ id: $props.currentId });
  if (res.code === 0) {
    if (res.data.length > 0) {
      infoTableData.value = (res.data || []).map(item => ({
        ...item,
        editing: false
      }));
    } else {
      infoTableData.value = [{ certificate: "", editing: false }];
    }
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

// 通过
const handlePass = async (index: number) => {
  const item = infoTableData.value[index];
  const res: any = await handleCertifications(item);
  if (res.code === 0) {
    item.editing = false;
    ElMessage.success("操作成功");
    $emit("update:updataList", true);
  } else {
    ElMessage.error(res.msg || "操作失败");
  }
};

// 驳回
function handleReject(index: number) {
  const item = infoTableData.value[index];
  item.editing = false;
}

function editCert(index: number) {
  infoTableData.value[index].editing = true;
  currentEditIndex.value = index;
}
function saveCert(index: number) {
  handlePass(index);
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <div>
        <div
          style="
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: left;
          "
        >
          资格证书
        </div>
        <el-row :gutter="24">
          <el-col
            v-for="(item, index) in infoTableData"
            :key="item.id || index"
            :span="12"
            style="margin-bottom: 16px"
          >
            <el-form
              :model="item"
              label-width="100px"
              :rules="rules"
              class="cert-item-form"
            >
              <el-row align="middle">
                <el-col :span="20">
                  <el-form-item label="证书名称" prop="certificate" required>
                    <div style="display: flex; align-items: center">
                      <el-input
                        v-model="item.certificate"
                        :disabled="!item.editing"
                        placeholder="请输入证书名称"
                        style="flex: 1; min-width: 220px; max-width: 340px"
                      />
                      <el-icon
                        v-if="!item.editing"
                        style="
                          cursor: pointer;
                          margin-left: 12px;
                          font-size: 20px;
                        "
                        title="编辑"
                        @click="editCert(index)"
                      >
                        <Edit />
                      </el-icon>
                      <el-icon
                        v-else
                        style="
                          color: #67c23a;
                          cursor: pointer;
                          margin-left: 12px;
                          font-size: 20px;
                        "
                        title="保存"
                        @click="saveCert(index)"
                      >
                        <Check />
                      </el-icon>
                      <el-icon
                        v-if="item.editing"
                        style="
                          color: #f56c6c;
                          cursor: pointer;
                          margin-left: 8px;
                          font-size: 20px;
                        "
                        title="取消"
                        @click="handleReject(index)"
                      >
                        <Close />
                      </el-icon>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
