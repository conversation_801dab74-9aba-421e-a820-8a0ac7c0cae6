export default {
  path: "/user",
  redirect: "/user/403",
  meta: {
    icon: "ri-user-fill",
    // showLink: false,
    title: "必审",
    rank: 9
  },
  children: [

    {
      path: "/company/settlement",
      meta: {
        title: "企业入驻"
      },
      children: [
        {
          path: "/company/settlement/awaiting_review",
          name: "settlement_awaiting_review",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/settlement/passed",
          name: "settlement_passed",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/settlement/rejected",
          name: "settlement_rejected",
          component: () =>
            import(
              "@/views/company_Information/enterprise_settlement/index.vue"
            ),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    },
    {
      path: "/company/address",
      meta: {
        title: "地址认证"
      },
      children: [
        {
          path: "/company/address/awaiting_review",
          name: "address_awaiting_review",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "待审核",
            businessStatus: 0
          }
        },
        {
          path: "/company/address/passed",
          name: "address_passed",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "已通过",
            businessStatus: 1
          }
        },
        {
          path: "/company/address/rejected",
          name: "address_rejected",
          component: () =>
            import(
              "@/views/company_Information/address_verification/index.vue"
            ),
          meta: {
            title: "已驳回",
            businessStatus: 2
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
