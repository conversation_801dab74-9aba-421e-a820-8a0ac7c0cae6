export default {
  path: "/info",
  redirect: "/user/403",
  meta: {
    icon: "ri-file-text-fill",
    // showLink: false,
    title: "信息管理",
    rank: 13
  },
  children: [
    {
      path: "/info/info_query",
      meta: {
        title: "基本信息"
      },
      children: [
        {
          path: "/info/info_query/user_info",
          name: "userInfo",
          component: () => import("@/views/Info_manage/user_info/index.vue"),
          meta: {
            title: "用户信息"
          }
        },
        {
          path: "/info/info_query/company_info",
          name: "companyInfo",
          component: () => import("@/views/Info_manage/company_info/index.vue"),
          meta: {
            title: "企业信息"
          }
        },
        {
          path: "/info/info_query/companyHr_info",
          name: "companyHrInfo",
          component: () => import("@/views/Info_manage/hr_info/index.vue"),
          meta: {
            title: "招聘者信息"
          }
        },
        {
          path: "/info/info_query/certification_info",
          name: "certificationInfo",
          component: () =>
            import("@/views/Info_manage/certification_info/index.vue"),
          meta: {
            title: "企业认证信息"
          }
        },
        {
          path: "/info/info_query/identity_info",
          name: "identityInfo",
          component: () =>
            import("@/views/Info_manage/identity_info/index.vue"),
          meta: {
            title: "身份信息"
          }
        },
        {
          path: "/info/info_query/jobs_info",
          name: "jobsInfo",
          component: () => import("@/views/Info_manage/jobs_info/index.vue"),
          meta: {
            title: "岗位信息"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
