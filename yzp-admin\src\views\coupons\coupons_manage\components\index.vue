<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  getPropCategory,
  addCoupons,
  updateCoupons
} from "@/api/coupons/index";
import { ElMessage } from "element-plus";
import { cityList } from "@/api/city";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const formRef = ref<any>(null);
const cityListData = ref<any>([]);
const proviceList = ref<any>([]);
const townList = ref<any>([]);
const propCategoryList = ref<any>([]);

const $emit = defineEmits(["cancelBtn", "update:dialogVisible"]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  isType: {
    type: String,
    default: "isAdd"
  }
});

const visible = computed({
  get() {
    if ($props.dialogVisible) {
      getPropCategoryData();
    }
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

const form = ref<any>({
  provinceCode: "",
  provinceName: "",
  cityCode: "",
  cityName: "",
  propId: "",
  propCategoryId: "",
  originalPrice: "",
  price: ""
});

const rules = ref<any>({
  provinceCode: [{ required: true, message: "请选择省份", trigger: "change" }],
  cityCode: [{ required: true, message: "请选择城市", trigger: "change" }],
  propId: [{ required: true, message: "请选择道具卡", trigger: "change" }],
  originalPrice: [
    { required: true, message: "请输入原价", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正数，最多两位小数"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  price: [
    { required: true, message: "请输入实际售价", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        const reg = /^(0|[1-9]\d*)(\.\d{1,2})?$/;
        if (!reg.test(value)) {
          callback(new Error("请输入正数，最多两位小数"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});

//处理城市数据
const deepCloneAndFilter = data => {
  // 深拷贝
  const cloned = JSON.parse(JSON.stringify(data));

  // 递归过滤 level === 3
  function filterLevel(arr) {
    return arr
      .filter(item => item.level === 1)
      .map(item => {
        if (item.childList && item.childList.length) {
          item.childList = filterLevel(item.childList);
        }
        return item;
      });
  }

  return filterLevel(cloned);
};

// 处理省份变化事件
const handleCityChange = item => {
  form.value.cityCode = "";
  townList.value =
    cityListData.value.find((items: any) => items.code === item)?.childList ||
    [];
  form.value.provinceName = proviceList.value.find(
    (items: any) => items.code === item
  )?.name;
};

// 处理城市变化事件
const handleTownChange = item => {
  form.value.cityName = townList.value.find(
    (items: any) => items.code === item
  )?.name;
};

// 处理道具卡变化事件
const handlePropChange = item => {
  form.value.propCategoryId = propCategoryList.value.find(
    (items: any) => items.id === item
  )?.id;
};

// 获取道具类别
const getPropCategoryData = async () => {
  const res: any = await getPropCategory();
  if (res.code === 0) {
    propCategoryList.value = res.data;
  }
};

// 获取详情
const getDetails = async () => {
  const row = $props.currentRow;

  // 设置省份 & 城市
  townList.value =
    cityList.find((province: any) => province.code === row.provinceCode)
      ?.childList || [];

  form.value = {
    provinceCode: row.provinceCode || "",
    provinceName:
      proviceList.value.find(i => i.code === row.provinceCode)?.name || "",
    cityCode: row.cityCode || "",
    cityName: townList.value.find(i => i.code === row.cityCode)?.name || "",
    propId: row.propCategoryId || "",
    propCategoryId: "",
    originalPrice: row.originalPrice / 100 || "",
    price: row.price / 100 || ""
  };
};

// 取消
function cancelBtn() {
  form.value = {};
  formRef.value.resetFields();
  $emit("cancelBtn", true);
}

// 取消
async function handleClose() {
  cancelBtn();
}

// 提交
const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const payload = {
      ...form.value,
      originalPrice: Math.round(Number(form.value.originalPrice) * 100),
      price: Math.round(Number(form.value.price) * 100)
    };
    const res: any =
      $props.isType === "isAdd"
        ? await addCoupons({ ...payload })
        : await updateCoupons({ ...payload, id: $props.currentRow.id });
    if (res.code === 0) {
      ElMessage.success("操作成功");
      cancelBtn();
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  } catch (err) {
    console.warn("表单校验未通过:", err);
  }
};

watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal) {
      cityListData.value = JSON.parse(JSON.stringify(cityList));
      proviceList.value = deepCloneAndFilter(cityList);
      getPropCategoryData();
      if ($props.isType === "isEdit" || $props.isType === "isDetails") {
        getDetails();
      }
    }
  },
  { immediate: true } // 如果有可能组件一加载就是 true，可以立即执行
);

onMounted(() => {
  cityListData.value = JSON.parse(JSON.stringify(cityList));
  proviceList.value = deepCloneAndFilter(cityList);
});
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      close-on-click-modal
      destroy-on-close
      :title="
        isType === 'isDetails' ? '详情' : isType === 'isEdit' ? '编辑' : '新增'
      "
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <!-- 详情内容区 -->

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        require-asterisk-position="right"
      >
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              label="省份："
              prop="provinceCode"
              label-position="right"
            >
              <el-select
                v-model="form.provinceCode"
                placeholder="请选择省份"
                style="width: 80%"
                :disabled="isType === 'isDetails' || isType === 'isEdit'"
                @change="handleCityChange"
              >
                <el-option
                  v-for="item in proviceList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市：" prop="cityCode" label-position="right">
              <el-select
                v-model="form.cityCode"
                placeholder="请选择城市"
                style="width: 80%"
                :disabled="isType === 'isDetails' || isType === 'isEdit'"
                @change="handleTownChange"
              >
                <el-option
                  v-for="item in townList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="道具卡：" prop="propId" label-position="right">
              <el-select
                v-model="form.propId"
                placeholder="请选择道具卡"
                style="width: 80%"
                :disabled="isType === 'isDetails' || isType === 'isEdit'"
                @change="handlePropChange"
              >
                <el-option
                  v-for="item in propCategoryList"
                  :key="item.id"
                  :label="item.categoryName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="原价："
              prop="originalPrice"
              label-position="right"
            >
              <el-input
                v-model="form.originalPrice"
                placeholder="请输入原价"
                :disabled="isType === 'isDetails'"
                style="width: 80%"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item
              label="实际售价："
              prop="price"
              label-position="right"
            >
              <el-input
                v-model="form.price"
                placeholder="请输入实际售价"
                :disabled="isType === 'isDetails'"
                style="width: 80%"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- 按钮组 -->
      <div v-if="rightType === 'note'" class="btn-group-bottom">
        <el-button @click="handleClose()">取消</el-button>
        <el-button type="primary" @click="handleSubmit()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}

.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.job-desc {
  margin-bottom: 5px !important;
  margin-top: 10px !important;
}
</style>
