<script setup lang="ts">
import { ref, watch } from "vue";

const props = defineProps<{
  options: Array<{ label: string; value: string; content: string }>;
  modelValue?: string;
}>();
const emit = defineEmits(["update:modelValue", "change", "selectedChange"]);

const selected = ref(null);
const editableText = ref(null);

// 当选择项变化时，更新文本内容
watch(selected, val => {
  const found = props.options.find(opt => opt.value === val);
  editableText.value = found ? found.content : "";
  emit("change", val);
  emit("selectedChange", val);
  emit("update:modelValue", editableText.value);
});

// 当外部modelValue变化时，更新编辑内容
watch(
  () => props.modelValue,
  val => {
    if (val !== undefined) editableText.value = val;
  }
);

// 编辑内容变化时，向外同步
watch(editableText, val => {
  emit("update:modelValue", val);
});
</script>

<template>
  <div class="reason-edit-card">
    <el-select v-model="selected" class="reason-select" style="width: 220px">
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-input
      v-model="editableText"
      placeholder="请填写批注内容"
      type="textarea"
      :rows="7"
      class="reason-textarea"
      resize="none"
      :autosize="{ minRows: 7, maxRows: 12 }"
    />
  </div>
</template>

<style scoped lang="scss">
.reason-edit-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.06);
  padding: 28px 32px 32px 32px;
  min-width: 420px;
  max-width: 700px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.reason-select {
  background: #f5f7fa;
  border-radius: 6px;
  .el-input__wrapper {
    background: #f5f7fa !important;
    border-radius: 6px !important;
  }
}
.reason-textarea {
  margin-top: 8px;
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
</style>
