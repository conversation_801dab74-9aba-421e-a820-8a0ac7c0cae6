<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { handlePositionAudit } from "@/api/auditModule/index";
import rawCity from "@/utils/city.json";

const loading = ref<boolean>(false);
const rightType = ref<any>("note");
const infoTableData = ref<any>({});
const avatarUrl = ref<any>("");

// 递归转换 childList 为 children
function convertCityData(list: any[]) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children: item.childList ? convertCityData(item.childList) : undefined
    };
    delete newItem.childList;
    return newItem;
  });
}

// 城市数据
const city = ref<any[]>([]);
let cityLoaded = false;
const cityLoading = ref(false);

async function loadCity() {
  if (!cityLoaded) {
    cityLoading.value = true;
    city.value = convertCityData(rawCity.zpData.cityList);
    cityLoaded = true;
    cityLoading.value = false;
  }
}

// 级联选择器配置
const cascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: true // 允许选择市或区
};

// 选中后获取省市区 code 和 name
function findNodesByCodes(codes: string[], nodes: any[]): any[] {
  let result: any[] = [];
  let currentNodes = nodes;
  for (const code of codes) {
    const node = currentNodes.find((n: any) => n.code === code);
    if (node) {
      result.push(node);
      currentNodes = node.children || [];
    } else {
      break;
    }
  }
  return result;
}

// 处理工作地址变化
function handleLocationChange(val: string[]) {
  const selectedData = findNodesByCodes(val, city.value);
  if (selectedData.length === 1) {
    // 只选了省，不允许
    ElMessage.warning("请选择市或区");
    return;
  }
  if (selectedData.length === 2 || selectedData.length === 3) {
    // 选了市或区
    infoTableData.value.provinceCode = selectedData[0]?.code || "";
    infoTableData.value.provinceName = selectedData[0]?.name || "";
    infoTableData.value.cityCode = selectedData[1]?.code || "";
    infoTableData.value.cityName = selectedData[1]?.name || "";
    infoTableData.value.districtCode = selectedData[2]?.code || "";
    infoTableData.value.districtName = selectedData[2]?.name || "";
  } else {
    infoTableData.value.provinceCode = "";
    infoTableData.value.provinceName = "";
    infoTableData.value.cityCode = "";
    infoTableData.value.cityName = "";
    infoTableData.value.districtCode = "";
    infoTableData.value.districtName = "";
  }
}

const auditList = ref<
  {
    auditUserName: string;
    auditTime: string;
    status: string;
    reason?: string;
  }[]
>([]);
const statusList = ref<{ person: string; time: string; person2: string }[]>([]);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentRow: {
    type: Object,
    default: () => ({})
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

const formRef = ref();
const rules = {
  // positionCode: [
  //   { required: true, message: "请输入岗位编码", trigger: "blur" },
  //   { pattern: /^\d+$/, message: "岗位编码只能是数字", trigger: "blur" }
  // ],
  // positionName: [
  //   { required: true, message: "请输入岗位名称", trigger: "blur" }
  // ],
  // workEducational: [
  //   { required: true, message: "请选择学历要求", trigger: "change" }
  // ],
  // workExperience: [
  //   { required: true, message: "请选择工作经验", trigger: "change" }
  // ],
  // workSalaryBegin: [
  //   { required: true, message: "请选择起始薪资", trigger: "change" }
  // ],
  // workSalaryEnd: [
  //   { required: true, message: "请选择结束薪资", trigger: "change" }
  // ],
  // workLocation: [
  //   { required: true, message: "请选择工作地址", trigger: "change" }
  // ],
  // positionDesc: [{ required: true, message: "请输入岗位描述", trigger: "blur" }]
};

const workEducationalList = ref<any[]>([
  { label: "不限", value: 0 },
  { label: "高中", value: 1 },
  { label: "专科", value: 2 },
  { label: "本科", value: 3 },
  { label: "硕士", value: 4 },
  { label: "博士", value: 5 }
]);
const workExperienceList = ref<any[]>([
  { label: "不限", value: 0 },
  { label: "1年以内", value: 1 },
  { label: "1-3年", value: 2 },
  { label: "3-5年", value: 3 },
  { label: "5-10年", value: 4 },
  { label: "10年以上", value: 5 }
]);

// 新增：薪资选项 1k-50k
const salaryOptions = ref<any[]>(
  Array.from({ length: 50 }, (_, i) => ({
    label: `${i + 1}k`,
    value: (i + 1) * 1000
  }))
);
// 新增：结束薪资选项 1k-60k
const salaryEndOptions = ref<any[]>(
  Array.from({ length: 60 }, (_, i) => ({
    label: `${i + 1}k`,
    value: (i + 1) * 1000
  }))
);

const workExperienceMap = [
  { value: 0, start: 0, end: 0 }, // 不限
  { value: 1, start: 0, end: 1 }, // 1年以内
  { value: 2, start: 1, end: 3 }, // 1-3年
  { value: 3, start: 3, end: 5 }, // 3-5年
  { value: 4, start: 5, end: 10 }, // 5-10年
  { value: 5, start: 10, end: 99 } // 10年以上
];

watch(
  () => $props.isRightType,
  newVal => {
    rightType.value = newVal;
  },
  { immediate: true }
);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
  rightType.value = "note";
}

// 审批
const handleAudit = async (params: any) => {};

// 通过
const handlePass = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    if (infoTableData.value.workSalaryEnd < infoTableData.value.workSalaryBegin) {
      ElMessage.warning("结束薪资不能小于起始薪资");
      return;
    }

    // 公共分隔符正则
    const splitRegex = /[，、；;|.\s]+/g;

    // 处理 positionBenefitList → 数组
    if (infoTableData.value.positionBenefitList) {
      if (typeof infoTableData.value.positionBenefitList === 'string') {
        infoTableData.value.positionBenefitList = infoTableData.value.positionBenefitList
          .split(splitRegex)
          .map(item => item.trim())
          .filter(Boolean);
      } else if (Array.isArray(infoTableData.value.positionBenefitList)) {
        // 如果已经是数组，就直接清理一下
        infoTableData.value.positionBenefitList = infoTableData.value.positionBenefitList
          .map(item => String(item).trim())
          .filter(Boolean);
      } else {
        // 其他类型直接转成空数组，避免报错
        infoTableData.value.positionBenefitList = [];
      }
    }


    // 处理 positionKey → 保留字符串，但分隔符统一成英文逗号
    if (infoTableData.value.positionKey) {
      infoTableData.value.positionKey = infoTableData.value.positionKey
        .replace(splitRegex, ",")
        .replace(/,+/g, ",")
        .replace(/^,|,$/g, "");
    }

    infoTableData.value.id = $props.currentRow.id;

    const submitData = { ...infoTableData.value };
    delete submitData.workExperience;
    delete submitData.workLocation;

    const res: any = await handlePositionAudit(submitData);
    if (res.code === 0) {
      ElMessage.success("操作成功");
      cancelBtn();
      $emit("update:updataList", true);
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  });
};


// 取消
function handleReject() {
  cancelBtn();
  infoTableData.value = {};
}

function mapWorkExperience(start: number, end: number) {
  if (start === 0 && end === 0) return 0;
  if (end >= 10) return 5;
  if (start === 0 && end === 1) return 1;
  if (start === 1 && end === 3) return 2;
  if (start === 3 && end === 5) return 3;
  if (start === 5 && end === 10) return 4;
  return 0; // 默认不限
}

watch(
  () => infoTableData.value.workExperience,
  val => {
    const found = workExperienceMap.find(item => item.value === val);
    if (found) {
      infoTableData.value.workExperienceStart = found.start;
      infoTableData.value.workExperienceEnd = found.end;
    }
  }
);

watch(
  () => $props.currentRow,
  newVal => {
    auditList.value = [];
    statusList.value = [];
    auditList.value.push({
      auditUserName: newVal?.manualInspectionUserName || "",
      auditTime: newVal?.manualInspectionTime || "",
      status: newVal?.status === 1 ? "1" : newVal?.status === 2 ? "2" : "0",
      reason: newVal?.reason || ""
    });
    infoTableData.value = newVal;
    avatarUrl.value = newVal.headImgUrl;
    infoTableData.value.workExperience = mapWorkExperience(
      newVal.workExperienceStart,
      newVal.workExperienceEnd
    );

    infoTableData.value.positionBenefitStr = (newVal?.positionBenefitList || []).join(",");
    // 初始化工作地址
    if (newVal?.provinceCode && newVal?.cityCode) {
      const workLocation = [newVal.provinceCode, newVal.cityCode];
      if (newVal?.districtCode) {
        workLocation.push(newVal.districtCode);
      }
      infoTableData.value.workLocation = workLocation;
    } else {
      infoTableData.value.workLocation = [];
    }

    // 加载城市数据
    loadCity();
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <div>
        <div
          style="
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: left;
          "
        >
          岗位信息
        </div>
        <el-form
          ref="formRef"
          :model="infoTableData"
          label-width="120px"
          :rules="rules"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="岗位编码" prop="positionCode">
                <el-input
                  v-model="infoTableData.positionCode"
                  placeholder="请输入岗位编码"
                />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="岗位名称" prop="positionName">
                <el-input
                  v-model="infoTableData.positionName"
                  placeholder="请输入岗位名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位子标签编码" prop="positionMarkCode">
                <el-input
                  v-model="infoTableData.positionMarkCode"
                  placeholder="请输入岗位子标签编码"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="岗位子标签" prop="positionMarkName">
                <el-input
                  v-model="infoTableData.positionMarkName"
                  placeholder="请输入岗位子标签"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学历要求" prop="workEducational">
                <el-select
                  v-model="infoTableData.workEducational"
                  placeholder="请选择学历要求"
                >
                  <el-option
                    v-for="item in workEducationalList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作经验" prop="workExperience">
                <el-select
                  v-model="infoTableData.workExperience"
                  placeholder="请选择工作经验"
                >
                  <el-option
                    v-for="item in workExperienceList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="起始薪资" prop="workSalaryBegin">
                <el-select
                  v-model="infoTableData.workSalaryBegin"
                  placeholder="请选择起始薪资"
                >
                  <el-option
                    v-for="item in salaryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结束薪资" prop="workSalaryEnd">
                <el-select
                  v-model="infoTableData.workSalaryEnd"
                  placeholder="请选择结束薪资"
                >
                  <el-option
                    v-for="item in salaryEndOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="工作地址" prop="workLocation">
                <el-cascader
                  v-model="infoTableData.workLocation"
                  :options="city"
                  :props="cascaderProps"
                  :loading="cityLoading"
                  placeholder="请选择工作地址"
                  style="width: 100%"
                  @change="handleLocationChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="招聘者姓名">
                <el-input
                  v-model="infoTableData.createUserName"
                  placeholder="请输入招聘者姓名"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提交时间">
                <el-date-picker
                  v-model="infoTableData.createTime"
                  type="datetime"
                  placeholder="请选择提交时间"
                  style="width: 100%"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="岗位关键词" prop="positionKey">
                <el-input
                  v-model="infoTableData.positionKey"
                  type="textarea"
                  placeholder="请输入岗位关键词，使用英文逗号分割（例如：本科,朝九晚六）"
                  :autosize="{ minRows: 2 }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="福利待遇" prop="positionBenefitList">
                <el-input
                  v-model="infoTableData.positionBenefitList"
                  type="textarea"
                  placeholder="请输入福利待遇，使用英文逗号分割（例如：双休, 五险一金）"
                  :autosize="{ minRows: 2 }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="岗位描述" prop="positionDesc" >
                <el-input
                  v-model="infoTableData.positionDesc"
                  type="textarea"
                  placeholder="请输入岗位描述"
                  :autosize="{ minRows: 4 }"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 按钮组 -->
      <div class="btn-group-bottom">
        <el-button @click="() => handleReject()">取消</el-button>
        <el-button type="success" @click="() => handlePass()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
