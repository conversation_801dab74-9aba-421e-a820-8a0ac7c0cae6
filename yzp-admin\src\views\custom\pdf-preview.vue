<template>
  <div class="pdf-preview-page">
    <div class="pdf-header">
      <div class="pdf-title">
        <el-icon><Document /></el-icon>
        <span>PDF预览</span>
      </div>
      <div class="pdf-actions">
        <el-button type="primary" @click="downloadPdf">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button @click="closeWindow">
          <el-icon><Close /></el-icon>
          关闭
        </el-button>
      </div>
    </div>
    <div class="pdf-content">
      <iframe
        v-if="pdfUrl"
        :src="pdfUrl"
        class="pdf-iframe"
        frameborder="0"
      ></iframe>
      <div v-else class="pdf-error">
        <el-icon><Warning /></el-icon>
        <span>PDF加载失败</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute } from "vue-router";
import { Document, Download, Close, Warning } from "@element-plus/icons-vue";

const route = useRoute();
const pdfUrl = ref("");

onMounted(() => {
  // 从URL参数获取PDF地址
  const url = route.query.url as string;
  if (url) {
    pdfUrl.value = decodeURIComponent(url);
  }
});

const downloadPdf = () => {
  if (pdfUrl.value) {
    const link = document.createElement("a");
    link.href = pdfUrl.value;
    link.download = "document.pdf";
    link.click();
  }
};

const closeWindow = () => {
  window.close();
};
</script>

<style lang="scss" scoped>
.pdf-preview-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f6fa;
}

.pdf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pdf-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #222;
}

.pdf-actions {
  display: flex;
  gap: 12px;
}

.pdf-content {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  color: #909399;
  font-size: 16px;
}
</style>
