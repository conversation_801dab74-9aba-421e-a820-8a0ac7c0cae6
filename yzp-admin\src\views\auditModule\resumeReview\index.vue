<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import TableDetails from "./components/index.vue";
import AttachmentSet from "./components/AttachmentSet.vue";
import JobExpectations from "./components/JobExpectations.vue";
import ProjectExperience from "./components/ProjectExperience.vue";
import Education from "./components/Education.vue";
import WorkExperience from "./components/WorkExperience.vue";
import Certifications from "./components/Certifications.vue";

import { useRoute } from "vue-router";
import { formatTimestamp } from "@/utils/dateFormat";
import type { ComponentSize } from "element-plus";
import { getResumeList } from "@/api/auditModule/index";
import cityData from "@/utils/city.json";

const form = ref<any>({
  provinceName: "",
  cityName: "",
  districtName: ""
});
const detailsDialogVisible = ref(false);
const attachmentSetVisible = ref(false);
const jobExpectationsVisible = ref(false);
const projectExperienceVisible = ref(false);
const educationVisible = ref(false);
const workExperienceVisible = ref(false);
const certificationsVisible = ref(false);

const isRightType = ref("");
const loading = ref(false);
const closeOnClickModal = ref(false);

let formQuery = [
  {
    type: "input",
    key: "trueName",
    label: "姓名"
  },
  {
    type: "input",
    key: "expectedPositions",
    label: "期望岗位"
  },
  {
    type: "select",
    key: "provinceName",
    label: "省份",
    options: []
  },
  {
    type: "select",
    key: "cityName",
    label: "市",
    options: []
  },
  {
    type: "select",
    key: "districtName",
    label: "区/县",
    options: []
  }
  // {
  //   type: "datetime",
  //   key: "dates",
  //   label: "提交时间"
  // }
];

interface Cms {
  property: string;
  label: string;
  width: string;
}
const tableCms: Cms[] = [
  {
    property: "id",
    label: "数据id",
    width: "",
    width: "100"
  },
  {
    property: "userId",
    label: "用户id",
    width: "",
    width: "100"
  },
  {
    property: "trueName",
    label: "姓名",
    width: ""
  },
  {
    property: "phone",
    label: "手机号码",
    width: ""
  },
  {
    property: "gender",
    label: "性别",
    width: ""
  },
  {
    property: "myLights",
    label: "个人亮点",
    width: ""
  },
  {
    property: "seekStatus",
    label: "求职状态",
    width: ""
  },
  {
    property: "expectedPositions",
    label: "期望岗位",
    width: ""
  },
  {
    property: "createTime",
    label: "提交时间",
    width: ""
  }
];
const tableData = ref<any[]>([]);

const currentPage = ref(1);
const pageSize = ref(10);
const size = ref<ComponentSize>("default");
const background = ref(false);
const disabled = ref(false);
const total = ref(0);
const currentRow = ref<any>({});
const currentId = ref<any>(null);

const route = useRoute();

// 分页接口入参
const params = ref<any>({
  entity: {
    createUserName: null,
    endTime: null,
    name: null,
    startTime: null,
    status: null,
    provinceName: null,
    cityName: null,
    districtName: null,
    expectedPositions: null
  },
  orderBy: {},
  page: 1,
  size: 10
});

// 求职状态处理
const handleSeekStatus = (row: any) => {
  const status = row.seekStatus;
  if (status === 0) {
    return "离职-随时到岗";
  } else if (status === 1) {
    return "在职-近期到岗";
  } else if (status === 2) {
    return "在职-观望机会";
  } else {
    return "";
  }
};

const getResumeData = async () => {
  loading.value = true;
  try {
    const res: any = await getResumeList(params.value);
    if (res.code === 0) {
      total.value = res.data.total;
      tableData.value = res.data.list;
    }
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = val => {
  pageSize.value = val;
  params.value.size = val;
  params.value.page = 1;
  getResumeData();
};
const handleCurrentChange = val => {
  currentPage.value = val;
  params.value.page = val;
  getResumeData();
};

const headerHeight: number = 420;
const tableHeight = ref(window.innerHeight - headerHeight); // 计算高度，headerHeight是你页面顶部栏的高度
function resizeTableHeight() {
  tableHeight.value = window.innerHeight - headerHeight; // 更新表格高度
}

function handleInfo(item) {
  currentId.value = item.row.id;
  detailsDialogVisible.value = true;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  closeOnClickModal.value = false;
  isRightType.value = "note";
}

const handleExpectations = item => {
  currentId.value = item.row.id;
  currentRow.value = item.row;
  jobExpectationsVisible.value = true;
  detailsDialogVisible.value = false;
  attachmentSetVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  closeOnClickModal.value = false;
};

const handleEducation = item => {
  currentId.value = item.row.id;
  educationVisible.value = true;
  detailsDialogVisible.value = false;
  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  closeOnClickModal.value = false;
};

const handleWorkExperience = item => {
  currentId.value = item.row.id;
  workExperienceVisible.value = true;
  detailsDialogVisible.value = false;
  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  certificationsVisible.value = false;
  closeOnClickModal.value = false;
};

const handleProjectExperience = item => {
  currentId.value = item.row.id;
  projectExperienceVisible.value = true;
  detailsDialogVisible.value = false;
  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  closeOnClickModal.value = false;
};

const handleCertifications = item => {
  currentId.value = item.row.id;
  certificationsVisible.value = true;
  detailsDialogVisible.value = false;
  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  closeOnClickModal.value = false;
};
const handleAttachmentSet = item => {
  currentId.value = item.row.id;
  attachmentSetVisible.value = true;
  detailsDialogVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  closeOnClickModal.value = false;
};

const handleCancelBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  closeOnClickModal.value = false;

  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
};

const handleConfirmBtn = () => {
  isRightType.value = "";
  detailsDialogVisible.value = false;
  closeOnClickModal.value = false;

  attachmentSetVisible.value = false;
  jobExpectationsVisible.value = false;
  projectExperienceVisible.value = false;
  educationVisible.value = false;
  workExperienceVisible.value = false;
  certificationsVisible.value = false;
  getResumeData();
};

// 搜索
const handleSearch = () => {
  if (form.value.dates && form.value.dates.length === 2) {
    params.value.entity.startTime = form.value.dates[0];
    params.value.entity.endTime = form.value.dates[1];
  } else {
    delete params.value.entity.startTime;
    delete params.value.entity.endTime;
  }
  params.value.entity.trueName = form.value.trueName || undefined;
  params.value.entity.positionName = form.value.positionName || undefined;
  params.value.entity.expectedPositions =
    form.value.expectedPositions || undefined;

  // 处理省市区字段，如果选择"全部"（空字符串）则不传该参数
  if (form.value.provinceName && form.value.provinceName !== "") {
    params.value.entity.provinceCode = form.value.provinceName;
  } else {
    delete params.value.entity.provinceCode;
  }

  if (form.value.cityName && form.value.cityName !== "") {
    params.value.entity.cityCode = form.value.cityName;
  } else {
    delete params.value.entity.cityCode;
  }

  if (form.value.districtName && form.value.districtName !== "") {
    params.value.entity.districtCode = form.value.districtName;
  } else {
    delete params.value.entity.districtCode;
  }

  // status 不变
  getResumeData();
};
// 重置
const handleReset = () => {
  form.value = {
    provinceName: "",
    cityName: "",
    districtName: ""
  };
  params.value = {
    entity: {
      createUserName: null,
      endTime: null,
      positionName: null,
      startTime: null
    },
    orderBy: {},
    page: 1,
    size: 10
  };

  // 确保删除省市区字段
  delete params.value.entity.provinceName;
  delete params.value.entity.cityName;
  delete params.value.entity.districtName;

  getResumeData();
};

// 省市区数据
const provinceList = ref<any[]>([]);
const cityList = ref<any[]>([]);
const districtList = ref<any[]>([]);

// 初始化省市区数据
const initCityData = () => {
  provinceList.value = cityData.zpData.cityList.map(provinceName => ({
    code: provinceName.code,
    name: provinceName.name
  }));

  // 更新formQuery中的省份选项
  const provinceItem = formQuery.find(item => item.key === "provinceName");
  if (provinceItem) {
    provinceItem.options = provinceList.value;
  }
};

// 根据省份获取城市列表
const getCityList = (provinceCode: string) => {
  const provinceName = cityData.zpData.cityList.find(
    p => p.code === provinceCode
  );
  if (provinceName && provinceName.childList) {
    cityList.value = provinceName.childList.map(cityName => ({
      code: cityName.code,
      name: cityName.name
    }));
  } else {
    cityList.value = [];
  }

  // 更新formQuery中的城市选项
  const cityItem = formQuery.find(item => item.key === "cityName");
  if (cityItem) {
    cityItem.options = cityList.value;
  }

  // 清空区和市的选择
  form.value.cityName = "";
  form.value.districtName = "";
  districtList.value = [];
};

// 根据城市获取区县列表
const getAreaList = (cityCode: string) => {
  const provinceName = cityData.zpData.cityList.find(
    p => p.code === form.value.provinceName
  );
  if (provinceName && provinceName.childList) {
    const cityName = provinceName.childList.find(c => c.code === cityCode);
    if (cityName && cityName.childList) {
      districtList.value = cityName.childList.map(districtName => ({
        code: districtName.code,
        name: districtName.name
      }));
    } else {
      districtList.value = [];
    }
  } else {
    districtList.value = [];
  }

  // 更新formQuery中的区县选项
  const areaItem = formQuery.find(item => item.key === "districtName");
  if (areaItem) {
    areaItem.options = districtList.value;
  }

  // 清空区的选择
  form.value.districtName = "";
};

// 省份变化处理
const handleProvinceChange = (provinceCode: string) => {
  getCityList(provinceCode);
};

// 城市变化处理
const handleCityChange = (cityCode: string) => {
  getAreaList(cityCode);
};

onMounted(() => {
  getResumeData();
  initCityData(); // 初始化省市区数据
  window.addEventListener("resize", resizeTableHeight);
});
onUnmounted(() => {
  window.removeEventListener("resize", resizeTableHeight);
});
</script>

<template>
  <div>
    <el-card shadow="never">
      <div class="table-header-flex">
        <el-form :inline="true" :model="form" class="table-header-form">
          <el-form-item
            v-for="(item, index) in formQuery"
            :key="index"
            :label="item.label"
            class="form-item"
          >
            <el-input
              v-if="item.type === 'input'"
              v-model="form[item.key]"
              :placeholder="'请输入' + item.label"
              style="min-width: 170px"
              clearable
            />
            <el-select
              v-else-if="item.type === 'select'"
              v-model="form[item.key]"
              :placeholder="'请选择' + item.label"
              style="min-width: 170px"
              clearable
              @change="
                item.key === 'provinceName'
                  ? handleProvinceChange(form[item.key])
                  : item.key === 'cityName'
                    ? handleCityChange(form[item.key])
                    : null
              "
            >
              <!-- <el-option
                v-if="item.key === 'provinceName'"
                label="全部"
                value=""
              />
              <el-option v-if="item.key === 'cityName'" label="全部" value="" />
              <el-option
                v-if="item.key === 'districtName'"
                label="全部"
                value=""
              /> -->
              <el-option
                v-for="option in item.options"
                :key="option.code"
                :label="option.name"
                :value="option.code"
              />
            </el-select>
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.key]"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 380px"
              value-format="YYYY-MM-DD HH:mm:ss"
              clearable
            />
          </el-form-item>
        </el-form>
        <div class="form-btns">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button
            type="info"
            style="background-color: #b4c4d1; color: #ffffff; border: #b4c4d1"
            @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </el-card>
    <el-card shadow="never" style="margin-top: 15px; padding: 5px 15px">
      <el-table
        ref="tableContainer"
        :data="tableData"
        :loading="loading"
        style="width: 100%"
        border
        :height="tableHeight"
      >
        <!-- <el-table-column type="selection" width="60" /> -->
        <el-table-column
          v-for="(config, index) in tableCms"
          :key="index"
          :width="config.width"
          :label="config.label"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-if="config.property === 'createTime'">
              {{ formatTimestamp(scope.row[config.property]) }}
            </span>
            <span v-else-if="config.property === 'seekStatus'">
              {{ handleSeekStatus(scope.row) }}
            </span>
            <span v-else-if="config.property === 'gender'">
              {{ scope.row[config.property] === 1 ? "男" : "女" }}
            </span>

            <span v-else>
              {{ scope.row[config.property] }}
            </span>
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleInfo(scope)"
            >
              个人信息
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleExpectations(scope)"
            >
              求职期望
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleEducation(scope)"
            >
              教育经历
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleWorkExperience(scope)"
            >
              工作经历
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleProjectExperience(scope)"
            >
              项目经历
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleCertifications(scope)"
            >
              资格证书
            </el-button>
            <el-button
              link
              type="primary"
              style="color: #279efb"
              @click="handleAttachmentSet(scope)"
            >
              附件/作品集
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :size="size"
          :disabled="disabled"
          :background="background"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <TableDetails
      v-model:dialogVisible="detailsDialogVisible"
      :isRightType="isRightType"
      :currentId="currentId"
      :closeOnClickModal="closeOnClickModal"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <AttachmentSet
      v-model:dialogVisible="attachmentSetVisible"
      :currentId="currentId"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <JobExpectations
      v-model:dialogVisible="jobExpectationsVisible"
      :currentId="currentId"
      :currentRow="currentRow"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <ProjectExperience
      v-model:dialogVisible="projectExperienceVisible"
      :currentId="currentId"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <Education
      v-model:dialogVisible="educationVisible"
      :currentId="currentId"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <WorkExperience
      v-model:dialogVisible="workExperienceVisible"
      :currentId="currentId"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
    <Certifications
      v-model:dialogVisible="certificationsVisible"
      :currentId="currentId"
      @cancelBtn="handleCancelBtn"
      @update:updataList="handleConfirmBtn"
    />
  </div>
</template>
<style scoped lang="scss">
.table-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}
.table-header-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
}
.form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  padding-top: 12px;
}
.form-btns {
  display: flex;
  align-items: center;
  margin-left: 24px;
  gap: 12px;
}
</style>
