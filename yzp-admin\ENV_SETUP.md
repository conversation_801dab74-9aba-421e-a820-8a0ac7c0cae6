# 环境变量配置说明

## SM4加密密钥配置

本项目支持在不同环境中配置不同的SM4加密密钥。

### 开发环境配置

在项目根目录创建 `.env` 文件：

```bash
# 开发环境配置
VITE_SM4_KEY=2d2270e73313e8ce6ce07fa8b3a8c26f
```

### 生产环境配置

在项目根目录创建 `.env.production` 文件：

```bash
# 生产环境配置
VITE_SM4_KEY=your_production_sm4_key_here_32_chars
```

### 密钥要求

- SM4密钥必须是32位十六进制字符串
- 例如：`2d2270e73313e8ce6ce07fa8b3a8c26f`

### 使用方式

密钥会自动从环境变量中读取，无需手动配置。如果未找到环境变量，系统会使用默认密钥并显示警告信息。

### 修改的文件

1. **`src/hooks/useSmCrypto.ts`** - 更新加密解密函数，支持环境变量
2. **`src/utils/crypto.js`** - 更新加密解密函数，支持环境变量
3. **`types/global.d.ts`** - 添加 `VITE_SM4_KEY` 类型声明

### 注意事项

1. `.env` 和 `.env.production` 文件不应提交到版本控制系统
2. 请将这些文件添加到 `.gitignore` 中
3. 生产环境的密钥应该与开发环境不同，确保安全性
4. 密钥长度必须为32位十六进制字符串
