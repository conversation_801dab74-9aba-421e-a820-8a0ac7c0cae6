// 获取环境变量中的SM4密钥
const getSM4Key = () => {
  // 从环境变量获取密钥，开发环境使用.env，生产环境使用.env.production
  const envKey = import.meta.env.VITE_SM4_KEY;

  if (!envKey) {
    console.warn("未找到VITE_SM4_KEY环境变量，使用默认密钥");
    return "2d2270e73313e8ce6ce07fa8b3a8c26f"; // 默认密钥
  }
  return envKey;
};

// sm4 加密
export function encryption(params) {
  const SM4 = require("sm-crypto").sm4;
  const key = getSM4Key(); // 从环境变量获取密钥
  return SM4.encrypt(params, key);
}

// sm4 解密
export function decryption(params) {
  const SM4 = require("sm-crypto").sm4;
  const key = getSM4Key(); // 从环境变量获取密钥
  return SM4.decrypt(params, key); // 第一个参数是加密数据的编码，第二个参数是输出编码
}