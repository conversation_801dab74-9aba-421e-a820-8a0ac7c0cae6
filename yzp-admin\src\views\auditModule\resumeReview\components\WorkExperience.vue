<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  getWorkExperience,
  handleWorkExperience
} from "@/api/auditModule/index";
import dayjs from "dayjs";
import rawIndustry from "@/utils/industry.json";
import rawPosition from "@/utils/position.json";

const loading = ref<boolean>(false);
const infoTableData = ref<any[]>([]);

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentId: {
    type: Number,
    default: null
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

// 监听弹框显示状态变化，只在弹框打开时加载数据
watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal && $props.currentId) {
      loadDataAndExperience();
    }
  },
  { immediate: false }
);

// 加载数据并获取工作经验
async function loadDataAndExperience() {
  try {
    // 并行加载基础数据
    await Promise.all([loadIndustry(), loadPosition()]);

    // 基础数据加载完成后，再获取工作经验数据
    await getWorkExperienceData();
  } catch (error) {
    console.error("加载数据失败:", error);
  }
}

const formRefs = ref<any[]>([]);
const rules = {
  company: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
  // industryId: [
  //   { required: true, message: "请选择所在行业", trigger: "change" }
  // ],
  // timeRange: [{ required: true, message: "请选择在职时间", trigger: "change" }],
  // positionName: [
  //   { required: true, message: "请选择工作岗位", trigger: "change" }
  // ],
  // workDescription: [
  //   { required: true, message: "请输入工作内容", trigger: "blur" }
  // ],
  // workPerformance: [
  //   { required: true, message: "请输入工作业绩", trigger: "blur" }
  // ]
};

const getWorkExperienceData = async () => {
  const res: any = await getWorkExperience({ id: $props.currentId });
  if (res.code === 0) {
    if (Array.isArray(res.data) && res.data.length > 0) {
      infoTableData.value = res.data.slice(0, 20).map(item => {
        // 处理行业路径回显
        const industryPath = item.industryId
          ? findIndustryPathByCode(industryTree.value, item.industryId)
          : [];

        console.log("🚀 ~ 行业回显数据:", {
          industryId: item.industryId,
          industryTreeLength: industryTree.value?.length,
          industryPath
        });

        // 处理岗位路径回显
        const positionPath = item.positionCode
          ? findPositionPathByCode(positionTree.value, item.positionCode)
          : [];

        console.log("🚀 ~ 岗位回显数据:", {
          positionCode: item.positionCode,
          positionName: item.positionName,
          positionTreeLength: positionTree.value?.length,
          positionPath
        });

        return {
          ...item,
          timeRange: [
            dayjs(item.startTime).format("YYYY-MM"),
            dayjs(item.endTime).format("YYYY-MM")
          ],
          // v-model 绑定的路径数组
          industryPath: industryPath,
          positionPath: positionPath,

          // 详细信息用于提交
          industryDetail: item.industryId
            ? {
                code: item.industryId,
                name: item.industry || ""
              }
            : null,
          positionDetail: item.positionCode
            ? {
                code: item.positionCode,
                name: item.positionName || ""
              }
            : null
        };
      });
    } else {
      infoTableData.value = [
        {
          id: null,
          company: "",
          industryId: "",
          industry: "",
          industryPath: [],
          industryDetail: null,
          myLights: [],
          positionName: "",
          positionCode: "",
          positionPath: [],
          positionDetail: null,
          trueName: "",
          workDescription: "",
          timeRange: [],
          baseInfoId: $props.currentId
          // 其他字段可根据实际接口补充
        }
      ];
    }
  }
};

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

// 确认
const handlePass = async (id: number, index: number) => {
  const form = formRefs.value[index];
  if (!form) return;
  form.validate(async (valid: boolean) => {
    if (!valid) return;
    const currentData = infoTableData.value[index];

    // 构造提交数据，转换为后端期望的格式
    const submitData = {
      ...currentData,
      id: id,
      // 处理时间区间字段
      startTime: dayjs(currentData.timeRange[0], "YYYY-MM").valueOf(),
      endTime: dayjs(currentData.timeRange[1], "YYYY-MM").valueOf(),
      // 行业信息
      industryId: currentData.industryId || "",
      industry: currentData.industry || "",
      // 岗位信息
      positionCode: currentData.positionCode || "",
      positionName: currentData.positionName || ""
    };

    // 删除不需要提交的字段
    delete submitData.timeRange;
    delete submitData.industryPath;
    delete submitData.industryDetail;
    delete submitData.positionPath;
    delete submitData.positionDetail;
    const res: any = await handleWorkExperience(submitData);
    if (res.code === 0) {
      ElMessage.success(`工作经历${index + 1}修改成功`);
      cancelBtn();
      $emit("update:updataList", true);
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  });
};

// 取消
function handleReject() {
  cancelBtn();
}

const industryTree = ref<any[]>([]);
let industryLoaded = false;
const industryLoading = ref(false);

async function loadIndustry() {
  if (!industryLoaded) {
    industryLoading.value = true;
    const rawIndustry = await import("@/utils/industry.json");
    industryTree.value = convertIndustryData(rawIndustry.default.industryData);
    industryLoaded = true;
    industryLoading.value = false;
  }
}

function convertIndustryData(list: any[]) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children: item.childerIndustryData
        ? convertIndustryData(item.childerIndustryData)
        : undefined
    };
    delete newItem.childerIndustryData;
    return newItem;
  });
}

// 根据 code 递归查找节点
function findNodeByCode(nodes: any[], code: string | number): any {
  for (const node of nodes) {
    if (String(node.code) === String(code)) {
      return node;
    }
    if (node.children && node.children.length > 0) {
      const found = findNodeByCode(node.children, code);
      if (found) {
        return found;
      }
    }
  }
  return null;
}

// 递归查找行业路径
function findIndustryPathByCode(
  tree: any[],
  code: string | number,
  path: any[] = []
): any[] {
  if (!tree || !code) return [];

  for (const node of tree) {
    const currentPath = [...path, node.code];
    // 使用字符串比较，确保类型一致
    if (String(node.code) === String(code)) {
      return currentPath;
    }
    if (node.children) {
      const childPath = findIndustryPathByCode(
        node.children,
        code,
        currentPath
      );
      if (childPath.length) {
        return childPath;
      }
    }
  }
  return [];
}

// 递归查找岗位路径
function findPositionPathByCode(
  tree: any[],
  code: string | number,
  path: any[] = []
): any[] {
  if (!tree || !code) return [];

  for (const node of tree) {
    const currentPath = [...path, node.code];
    // 使用字符串比较，确保类型一致
    if (String(node.code) === String(code)) {
      return currentPath;
    }
    if (node.children) {
      const childPath = findPositionPathByCode(
        node.children,
        code,
        currentPath
      );
      if (childPath.length) {
        return childPath;
      }
    }
  }
  return [];
}

const industryCascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: false // 只能选到叶子
};

function handleIndustryChange(val: any, index: number) {
  // 确保 val 是数组
  const valArray = Array.isArray(val) ? val : [];

  if (valArray && valArray.length > 0) {
    // 获取最后一级的 code
    const lastCode = valArray[valArray.length - 1];

    // 根据 code 在 industryTree 中查找对应的节点信息
    const selectedNode = findNodeByCode(industryTree.value, lastCode);

    if (selectedNode) {
      // v-model 绑定路径数组（用于显示）
      infoTableData.value[index].industryPath = valArray;
      // 保存选中节点的详细信息（用于提交）
      infoTableData.value[index].industryDetail = {
        code: selectedNode.code,
        name: selectedNode.name
      };
    } else {
      infoTableData.value[index].industryPath = null;
      infoTableData.value[index].industryDetail = null;
    }
  } else {
    infoTableData.value[index].industryPath = null;
    infoTableData.value[index].industryDetail = null;
  }
}

const positionTree = ref<any[]>([]);
let positionLoaded = false;
const positionLoading = ref(false);

async function loadPosition() {
  if (!positionLoaded) {
    positionLoading.value = true;
    const rawPosition = await import("@/utils/position.json");
    positionTree.value = convertPositionData(
      rawPosition.default.zpData.position
    );
    positionLoaded = true;
    positionLoading.value = false;
  }
}

function convertPositionData(list: any[], depth = 1) {
  return list.map(item => {
    const newItem: any = {
      ...item,
      children:
        item.subLevelModelList && depth < 3
          ? convertPositionData(item.subLevelModelList, depth + 1)
          : undefined
    };
    delete newItem.subLevelModelList;
    return newItem;
  });
}

const positionCascaderProps = {
  label: "name",
  value: "code",
  children: "children",
  emitPath: true,
  checkStrictly: false // 只能选到叶子
};

function handlePositionChange(val: any, index: number) {
  // 确保 val 是数组
  const valArray = Array.isArray(val) ? val : [];

  if (valArray && valArray.length > 0) {
    // 获取最后一级的 code
    const lastCode = valArray[valArray.length - 1];

    // 根据 code 在 positionTree 中查找对应的节点信息
    const selectedNode = findNodeByCode(positionTree.value, lastCode);

    if (selectedNode) {
      // v-model 绑定路径数组（用于显示）
      infoTableData.value[index].positionPath = valArray;
      // 保存选中节点的详细信息（用于提交）
      infoTableData.value[index].positionDetail = {
        code: selectedNode.code,
        name: selectedNode.name
      };
    } else {
      infoTableData.value[index].positionPath = null;
      infoTableData.value[index].positionDetail = null;
    }
  } else {
    infoTableData.value[index].positionPath = null;
    infoTableData.value[index].positionDetail = null;
  }
}
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <div v-for="(item, index) in infoTableData" :key="item.id || index">
        <div>
          <div
            style="
              font-size: 20px;
              font-weight: bold;
              margin-bottom: 20px;
              text-align: left;
            "
          >
            {{ `工作经历${index + 1}` }}
          </div>
          <el-form
            :ref="el => (formRefs[index] = el)"
            :model="item"
            label-width="100px"
            :rules="rules"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称" prop="company" required>
                  <el-input
                    v-model="item.company"
                    placeholder="请输入公司名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="在职时间" prop="timeRange" >
                  <el-date-picker
                    v-model="item.timeRange"
                    type="monthrange"
                    range-separator="至"
                    start-placeholder="开始月份"
                    end-placeholder="结束月份"
                    format="YYYY-MM"
                    value-format="YYYY-MM"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
<!--              <el-col :span="12">-->
<!--                <el-form-item label="所在行业" prop="industryId" >-->
<!--                  <el-cascader-->
<!--                    v-model="item.industryPath"-->
<!--                    :options="industryTree"-->
<!--                    :props="industryCascaderProps"-->
<!--                    :loading="industryLoading"-->
<!--                    placeholder="请选择所在行业"-->
<!--                    style="width: 100%"-->
<!--                    filterable-->
<!--                    clearable-->
<!--                    :show-all-levels="false"-->
<!--                    @change="val => handleIndustryChange(val, index)"-->
<!--                  />-->
<!--                </el-form-item>-->
<!--              </el-col>-->


<!--              <el-col :span="12">-->
<!--                <el-form-item label="工作岗位" prop="positionName" >-->
<!--                  <el-cascader-->
<!--                    v-model="item.positionPath"-->
<!--                    :options="positionTree"-->
<!--                    :props="positionCascaderProps"-->
<!--                    :loading="positionLoading"-->
<!--                    placeholder="请选择工作岗位"-->
<!--                    style="width: 100%"-->
<!--                    filterable-->
<!--                    clearable-->
<!--                    :show-all-levels="false"-->
<!--                    @change="val => handlePositionChange(val, index)"-->
<!--                  />-->
<!--                </el-form-item>-->
<!--              </el-col>-->
              <el-col :span="12">
                <el-form-item label="行业编码" prop="industryId">
                  <el-input
                    v-model="item.industryId"
                    placeholder="请输入行业编码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="行业名称" prop="industry">
                  <el-input
                    v-model="item.industry"
                    placeholder="请输入行业名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="岗位编码" prop="positionCode">
                  <el-input
                    v-model="item.positionCode"
                    placeholder="请输入岗位编码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="岗位名称" prop="positionName">
                  <el-input
                    v-model="item.positionName"
                    placeholder="请输入岗位名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属部门" prop="department">
                  <el-input
                    v-model="item.department"
                    placeholder="请输入所属部门"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="工作内容" prop="workDescription" >
                  <el-input
                    v-model="item.workDescription"
                    type="textarea"
                    placeholder="请输入工作内容"
                    :autosize="{ minRows: 4 }"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="工作业绩" prop="workPerformance">
                  <el-input
                    v-model="item.workPerformance"
                    type="textarea"
                    placeholder="请输入工作业绩"
                    :autosize="{ minRows: 4 }"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <!-- 按钮组 -->
        <div class="btn-group-bottom">
          <el-button @click="() => handleReject()">取消</el-button>
          <el-button type="success" @click="() => handlePass(item.id, index)"
            >确认</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
