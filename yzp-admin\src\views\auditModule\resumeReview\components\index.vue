<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { handleResumeAudit, getPersonalInfo } from "@/api/auditModule/index";

const loading = ref<boolean>(false);
const infoTableData = ref<any>({});

const $emit = defineEmits([
  "cancelBtn",
  "update:dialogVisible",
  "update:updataList"
]);

const $props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  isRightType: {
    type: String,
    default: ""
  },
  currentId: {
    type: Number,
    default: null
  },
  closeOnClickModal: {
    type: Boolean,
    default: false
  }
});

const visible = computed({
  get() {
    return $props.dialogVisible;
  },
  set(value) {
    $emit("update:dialogVisible", value);
  }
});

const formRef = ref();
const rules = {
  // phone: [{ required: true, message: "请输入手机号码", trigger: "blur" }],
  // gender: [{ required: true, message: "请选择性别", trigger: "change" }],
  trueName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  seekStatus: [
    { required: true, message: "请选择求职状态", trigger: "change" }
  ],
  // skills: [{ required: true, message: "请输入掌握技能", trigger: "blur" }],
  myLights: [{ required: true, message: "请输入岗位描述", trigger: "blur" }]
};

const seekStatusList = ref<any[]>([
  { label: "离职-随时到岗", value: 0 },
  { label: "在职-近期到岗", value: 1 },
  { label: "在职-观望机会", value: 2 }
]);

const genderList = ref<any[]>([
  { label: "男", value: 1 },
  { label: "女", value: 2 }
]);

// 取消
function cancelBtn() {
  $emit("cancelBtn", true);
}

const getPersonalInfoData = async () => {
  const res: any = await getPersonalInfo({ id: $props.currentId });
  if (res.code === 0) {
    infoTableData.value = res.data || {};
  }
};

// 确认
const handlePass = async () => {
  formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;
    infoTableData.value.id = $props.currentId;

    // 统一替换 skills 字段的分隔符为英文逗号
    if (infoTableData.value.skills) {
      infoTableData.value.skills = infoTableData.value.skills.replace(
        /[，、；;| ]+/g,
        ","
      );
    }

    const submitData = { ...infoTableData.value };
    delete submitData.workExperience;
    const res: any = await handleResumeAudit(submitData);
    if (res.code === 0) {
      ElMessage.success("操作成功");
      cancelBtn();
      $emit("update:updataList", true);
    } else {
      ElMessage.error(res.msg || "操作失败");
    }
  });
};

// 取消
function handleReject() {
  cancelBtn();
}

// 监听弹框显示状态变化，只在弹框打开时加载数据
watch(
  () => $props.dialogVisible,
  newVal => {
    if (newVal && $props.currentId) {
      getPersonalInfoData();
    }
  },
  { immediate: false }
);
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      v-loading="loading"
      :close-on-click-modal="closeOnClickModal"
      destroy-on-close
      :title="null"
      width="1100px"
      :show-close="true"
      class="custom-detail-dialog"
      align-center
      @close="cancelBtn"
    >
      <div>
        <div
          style="
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: left;
          "
        >
          个人信息编辑
        </div>
        <el-form
          ref="formRef"
          :model="infoTableData"
          label-width="100px"
          :rules="rules"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="姓名" prop="trueName" required>
                <el-input
                  v-model="infoTableData.trueName"
                  placeholder="请输入姓名"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="提交时间" prop="createTime">
                <el-date-picker
                  v-model="infoTableData.createTime"
                  type="datetime"
                  placeholder="请选择提交时间"
                  style="width: 100%"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="phone">
                <el-input
                  v-model="infoTableData.phone"
                  placeholder="请输入手机号码"
                  disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="gender">
                <el-select
                  v-model="infoTableData.gender"
                  placeholder="请选择性别"
                  disabled
                >
                  <el-option
                    v-for="item in genderList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="求职状态" prop="seekStatus" required>
                <el-select
                  v-model="infoTableData.seekStatus"
                  placeholder="请选择求职状态"
                >
                  <el-option
                    v-for="item in seekStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="掌握技能" prop="skills" >
                <el-input
                  v-model="infoTableData.skills"
                  placeholder="请输入掌握技能，使用英文逗号分割（例如：测试数据1, 测试数据2）"
                  type="textarea"
                  :autosize="{ minRows: 2 }"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="个人亮点" prop="myLights" required>
                <el-input
                  v-model="infoTableData.myLights"
                  type="textarea"
                  placeholder="请输入岗位描述"
                  :autosize="{ minRows: 4 }"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <!-- 按钮组 -->
      <div class="btn-group-bottom">
        <el-button @click="() => handleReject()">取消</el-button>
        <el-button type="success" @click="() => handlePass()">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.custom-detail-dialog .el-dialog__body {
  background: #f5f6fa;
  padding-bottom: 0;
  min-height: 520px;
}
.info-table-top {
  background: #fff;
  border-radius: 10px;
  font-size: 16px;
}
.info-table-top .el-table__cell {
  border: none;
  background: #fff;
  padding: 18px 24px;
}
.content-row {
  display: flex;
  gap: 32px;
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 20px;
}
.card-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 24px 24px 18px 24px;
  min-width: 340px;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.card-title {
  font-size: 16px;
  color: #222;
  font-weight: 600;
  margin-bottom: 10px;
}
.cert-img {
  width: 100%;
  height: 220px;
  object-fit: contain;
  border-radius: 8px;
  background: #f7f8fa;
}
.note-select {
  margin-bottom: 12px;
}
.note-textarea {
  .el-textarea__inner {
    background: #f7f8fa;
    border-radius: 8px;
    font-size: 16px;
    color: #222;
    padding: 18px 16px;
    min-height: 180px;
    border: none;
    box-shadow: none;
    resize: none;
  }
}
.btn-group-bottom {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin: 20px 40px 0 0;
}
:deep(.el-table--fit) {
  margin-top: 10px;
  border-radius: 3px;
}
.img-card-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-direction: column;
  min-width: 48%;
}
@media (min-width: 900px) {
  .img-card-row {
    flex-direction: row;
  }
}
.img-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
// 当只有一张图片时，让它占满左侧区域
.img-card-row:has(.img-card:only-child) .img-card,
.img-card-row .img-card:only-child {
  width: 100%;
  max-width: 400px;
}
.img-title {
  font-size: 15px;
  color: #222;
  font-weight: 600;
  margin-bottom: 12px;
}
.img-preview {
  width: 180px;
  height: 180px;
  border-radius: 8px;
  background: #f5f6fa;
}
// 当只有一张图片时，调整图片尺寸
.img-card-row:has(.img-card:only-child) .img-preview,
.img-card-row .img-card:only-child .img-preview {
  width: 300px;
  height: 300px;
}
.img-placeholder {
  width: 180px;
  height: 180px;
  background: #e5e6eb;
  border-radius: 8px;
}
// 当只有一张图片时，调整占位符尺寸
.img-card-row:has(.img-card:only-child) .img-placeholder,
.img-card-row .img-card:only-child .img-placeholder {
  width: 300px;
  height: 300px;
}
// 暂无数据卡片样式
.no-data-card {
  background: #fff;
  border-radius: 10px;
  padding: 16px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 250px;
}
.no-data-text {
  color: #888;
  font-size: 16px;
  text-align: center;
}
.cell-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.cell-label {
  color: #888;
  font-size: 14px;
  margin-bottom: 6px;
}
.cell-value {
  color: #222;
  font-size: 18px;
  font-weight: 600;
  word-break: break-all;
}
.info-table-normal .el-table__cell {
  text-align: center;
  font-size: 16px;
}
.info-table-normal .el-table__header th {
  background: #f7f8fa;
  color: #888;
  font-weight: 500;
  font-size: 15px;
}
</style>
